// KFT Fitness Service Worker
// Lightning-fast PWA implementation with aggressive caching for instant loading

const CACHE_VERSION = '3.1-instant';
const STATIC_CACHE = `kft-static-v${CACHE_VERSION}`;
const DYNAMIC_CACHE = `kft-dynamic-v${CACHE_VERSION}`;
const RUNTIME_CACHE = `kft-runtime-v${CACHE_VERSION}`;
const VIDEO_CACHE = `kft-video-v${CACHE_VERSION}`;
const API_CACHE = `kft-api-v${CACHE_VERSION}`;

// Critical resources for instant loading
const CRITICAL_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.png',
  '/main.dart.js',
  '/flutter.js',
  '/flutter_bootstrap.js',
  '/assets/FontManifest.json',
  '/assets/AssetManifest.json',
  '/assets/AssetManifest.bin',
  '/assets/fonts/MaterialIcons-Regular.otf'
];

// Essential icons and splash screens
const ICON_ASSETS = [
  '/icons/Icon-192.png',
  '/icons/Icon-512.png',
  '/icons/Icon-maskable-192.png',
  '/icons/Icon-maskable-512.png',
  '/splash/img/light-1x.png',
  '/splash/img/light-2x.png',
  '/splash/img/light-3x.png',
  '/splash/img/light-4x.png',
  '/splash/img/dark-1x.png',
  '/splash/img/dark-2x.png',
  '/splash/img/dark-3x.png',
  '/splash/img/dark-4x.png'
];

// Flutter assets patterns for aggressive caching
const FLUTTER_PATTERNS = [
  /\/flutter_assets\//,
  /\/assets\//,
  /\/canvaskit\//,
  /\.dart\.js$/,
  /flutter\.js$/,
  /flutter_bootstrap\.js$/
];

// Static resource patterns (cache-first strategy)
const STATIC_PATTERNS = [
  /\.css$/,
  /\.woff2?$/,
  /\.ttf$/,
  /\.otf$/,
  /\.png$/,
  /\.jpg$/,
  /\.jpeg$/,
  /\.webp$/,
  /\.svg$/,
  /\.ico$/,
  /\.json$/
];

// API patterns (network-first with fallback)
const API_PATTERNS = [
  /\/api\//,
  /\/auth\//,
  /\/user\//,
  /\/courses\//,
  /\/videos\//,
  /\/progress\//
];

// Video content patterns
const VIDEO_PATTERNS = [
  /vimeo\.com/,
  /player\.vimeo\.com/,
  /\.mp4$/,
  /\.webm$/,
  /\.m3u8$/
];

// Install event - aggressive caching for instant loading
self.addEventListener('install', (event) => {
  console.log('🚀 KFT Fitness Service Worker installing with lightning-fast caching...');
  event.waitUntil(
    Promise.all([
      // Cache critical assets first for instant loading
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('⚡ Caching critical assets for instant startup');
        return cache.addAll(CRITICAL_ASSETS.map(url => new Request(url, { cache: 'reload' })));
      }),
      // Cache icons and splash screens
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('🎨 Caching icons and splash screens');
        return cache.addAll(ICON_ASSETS.map(url => new Request(url, { cache: 'reload' })));
      }),
      // Preload Flutter assets
      preloadFlutterAssets()
    ])
    .then(() => {
      console.log('✅ Lightning-fast Service Worker installed successfully');
      return self.skipWaiting();
    })
    .catch((error) => {
      console.error('❌ Service Worker installation failed:', error);
      // Continue with basic installation even if preloading fails
      return self.skipWaiting();
    })
  );
});

// Preload Flutter assets for instant loading
async function preloadFlutterAssets() {
  try {
    const cache = await caches.open(STATIC_CACHE);

    // Preload essential Flutter files
    const flutterAssets = [
      '/assets/packages/cupertino_icons/assets/CupertinoIcons.ttf',
      '/assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf',
      '/assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf',
      '/assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf',
      '/assets/assets/images/logo.webp'
    ];

    const requests = flutterAssets.map(url =>
      fetch(url, { cache: 'reload' }).catch(() => null)
    );

    const responses = await Promise.allSettled(requests);
    const validResponses = responses
      .filter(result => result.status === 'fulfilled' && result.value)
      .map(result => result.value);

    if (validResponses.length > 0) {
      await Promise.all(validResponses.map(response =>
        cache.put(response.url, response.clone())
      ));
      console.log(`📦 Preloaded ${validResponses.length} Flutter assets`);
    }
  } catch (error) {
    console.warn('⚠️ Flutter asset preloading failed:', error);
  }
}

// Activate event - clean up old caches and optimize performance
self.addEventListener('activate', (event) => {
  console.log('⚡ KFT Fitness Service Worker activating with performance optimizations...');
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        const validCaches = [STATIC_CACHE, DYNAMIC_CACHE, RUNTIME_CACHE, VIDEO_CACHE, API_CACHE];
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (!validCaches.includes(cacheName) && !cacheName.includes(`v${CACHE_VERSION}`)) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Warm up critical caches
      warmUpCaches(),
      // Claim all clients immediately
      self.clients.claim()
    ])
    .then(() => {
      console.log('✅ Lightning-fast Service Worker activated successfully');
      // Notify all clients about the new service worker
      self.clients.matchAll().then(clients => {
        clients.forEach(client => {
          client.postMessage({
            type: 'SW_ACTIVATED',
            version: CACHE_VERSION,
            timestamp: Date.now()
          });
        });
      });
    })
    .catch((error) => {
      console.error('❌ Service Worker activation failed:', error);
    })
  );
});

// Warm up caches for instant performance
async function warmUpCaches() {
  try {
    // Pre-warm runtime cache with likely requests
    const runtimeCache = await caches.open(RUNTIME_CACHE);

    // Pre-cache common API endpoints structure (empty responses for structure)
    const commonEndpoints = ['/api/user/profile', '/api/courses', '/api/progress'];

    console.log('🔥 Warming up caches for instant performance');
  } catch (error) {
    console.warn('⚠️ Cache warm-up failed:', error);
  }
}

// Fetch event - lightning-fast caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Only handle GET requests for caching
  if (request.method !== 'GET') {
    return;
  }

  // Handle different types of requests with optimized strategies
  event.respondWith(handleOptimizedRequest(request, url));
});

// Lightning-fast optimized request handling
async function handleOptimizedRequest(request, url) {
  try {
    // Critical assets: Instant cache-first for lightning speed
    if (CRITICAL_ASSETS.some(asset => url.pathname === asset || url.pathname.endsWith(asset))) {
      return await instantCacheFirst(request, STATIC_CACHE);
    }

    // Flutter assets: Aggressive cache-first
    if (FLUTTER_PATTERNS.some(pattern => pattern.test(url.pathname))) {
      return await instantCacheFirst(request, STATIC_CACHE);
    }

    // Static resources: Cache-first with background update
    if (STATIC_PATTERNS.some(pattern => pattern.test(url.pathname))) {
      return await cacheFirstWithBackgroundUpdate(request, STATIC_CACHE);
    }

    // Video content: Specialized video caching
    if (VIDEO_PATTERNS.some(pattern => pattern.test(url.href))) {
      return await videoCacheStrategy(request, VIDEO_CACHE);
    }

    // API calls: Network-first with intelligent caching
    if (API_PATTERNS.some(pattern => pattern.test(url.pathname))) {
      return await intelligentApiCache(request, API_CACHE);
    }

    // Default: Stale-while-revalidate for optimal performance
    return await staleWhileRevalidate(request, RUNTIME_CACHE);
  } catch (error) {
    console.error('❌ Optimized fetch failed:', error);
    return await getOfflineFallback(request);
  }
}

// Instant cache-first for lightning-fast loading
async function instantCacheFirst(request, cacheName) {
  // Check cache immediately for instant response
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  // If not in cache, fetch and cache for next time
  try {
    const networkResponse = await fetch(request, { cache: 'reload' });
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      // Don't await - cache in background for speed
      cache.put(request, networkResponse.clone()).catch(console.warn);
    }
    return networkResponse;
  } catch (error) {
    return await getOfflineFallback(request);
  }
}

// Cache-first with background update for optimal performance
async function cacheFirstWithBackgroundUpdate(request, cacheName) {
  const cachedResponse = await caches.match(request);

  if (cachedResponse) {
    // Return cached version immediately
    // Update in background for next time
    fetch(request).then(networkResponse => {
      if (networkResponse.ok) {
        caches.open(cacheName).then(cache => {
          cache.put(request, networkResponse.clone());
        });
      }
    }).catch(() => {}); // Silent background update

    return cachedResponse;
  }

  // Not in cache, fetch normally
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone()).catch(console.warn);
    }
    return networkResponse;
  } catch (error) {
    return await getOfflineFallback(request);
  }
}

// Stale-while-revalidate for optimal performance
async function staleWhileRevalidate(request, cacheName) {
  const cachedResponse = await caches.match(request);

  // Always try to update in background
  const networkPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      caches.open(cacheName).then(cache => {
        cache.put(request, networkResponse.clone());
      });
    }
    return networkResponse;
  }).catch(() => null);

  // Return cached version immediately if available
  if (cachedResponse) {
    return cachedResponse;
  }

  // If no cache, wait for network
  return networkPromise || await getOfflineFallback(request);
}

// Intelligent API caching with TTL
async function intelligentApiCache(request, cacheName) {
  const url = new URL(request.url);

  // Check for cached response with TTL
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    const cachedDate = cachedResponse.headers.get('sw-cached-date');
    if (cachedDate) {
      const age = Date.now() - parseInt(cachedDate);
      // Cache API responses for 5 minutes
      if (age < 5 * 60 * 1000) {
        return cachedResponse;
      }
    }
  }

  // Fetch fresh data
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      // Add timestamp header for TTL
      const responseToCache = new Response(networkResponse.body, {
        status: networkResponse.status,
        statusText: networkResponse.statusText,
        headers: {
          ...Object.fromEntries(networkResponse.headers.entries()),
          'sw-cached-date': Date.now().toString()
        }
      });

      const cache = await caches.open(cacheName);
      cache.put(request, responseToCache.clone()).catch(console.warn);
      return networkResponse;
    }
    return networkResponse;
  } catch (error) {
    // Return stale cache if network fails
    return cachedResponse || await getOfflineFallback(request);
  }
}

// Specialized video caching strategy
async function videoCacheStrategy(request, cacheName) {
  // For video content, use cache-first with longer TTL
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      // Only cache successful video responses
      const cache = await caches.open(cacheName);
      // Cache video content for longer periods
      cache.put(request, networkResponse.clone()).catch(console.warn);
    }
    return networkResponse;
  } catch (error) {
    return new Response('Video unavailable offline', {
      status: 503,
      headers: { 'Content-Type': 'text/plain' }
    });
  }
}

// Enhanced offline fallback with better UX
async function getOfflineFallback(request) {
  const url = new URL(request.url);

  // For HTML documents, return cached index with offline indicator
  if (request.destination === 'document' || request.headers.get('accept')?.includes('text/html')) {
    const cachedIndex = await caches.match('/index.html');
    if (cachedIndex) {
      return cachedIndex;
    }

    // Fallback offline page
    return new Response(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>KFT Fitness - Offline</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   text-align: center; padding: 50px; background: #3D5AFE; color: white; }
            .offline-icon { font-size: 64px; margin-bottom: 20px; }
          </style>
        </head>
        <body>
          <div class="offline-icon">📱</div>
          <h1>KFT Fitness</h1>
          <p>You're currently offline. Please check your internet connection.</p>
          <button onclick="location.reload()">Try Again</button>
        </body>
      </html>
    `, {
      status: 200,
      headers: { 'Content-Type': 'text/html' }
    });
  }

  // For API requests, return structured error
  if (url.pathname.startsWith('/api/')) {
    return new Response(JSON.stringify({
      error: 'offline',
      message: 'This feature requires an internet connection',
      timestamp: Date.now()
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // For other resources, return generic offline response
  return new Response('Resource unavailable offline', {
    status: 503,
    headers: { 'Content-Type': 'text/plain' }
  });
}

// Message event - handle messages from main thread
self.addEventListener('message', (event) => {
  console.log('📨 Service Worker received message:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Sync event - for background sync (if needed)
self.addEventListener('sync', (event) => {
  console.log('🔄 Background sync:', event.tag);
});

// Push notification handler
self.addEventListener('push', (event) => {
  console.log('📱 Push notification received');

  let notificationData = {
    title: 'KFT Fitness',
    body: 'You have a new update',
    icon: '/icons/Icon-192.png',
    badge: '/icons/Icon-192.png'
  };

  if (event.data) {
    try {
      notificationData = { ...notificationData, ...event.data.json() };
    } catch (e) {
      notificationData.body = event.data.text();
    }
  }

  event.waitUntil(
    self.registration.showNotification(notificationData.title, {
      body: notificationData.body,
      icon: notificationData.icon,
      badge: notificationData.badge,
      vibrate: [200, 100, 200],
      data: { url: notificationData.url || '/' }
    })
  );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 Notification clicked');
  event.notification.close();

  const urlToOpen = event.notification.data?.url || '/';

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // If app is already open, focus it
        for (const client of clientList) {
          if (client.url === urlToOpen && 'focus' in client) {
            return client.focus();
          }
        }
        // Otherwise open new window
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen);
        }
      })
  );
});

console.log('🏋️ KFT Fitness PWA Service Worker ready');
