<!DOCTYPE html><html><head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Professional fitness training app with personalized workouts, nutrition guidance, and progress tracking. Your pocket-sized personal trainer.">

  <!-- PWA Meta Tags -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="KFT Fitness">
  <meta name="application-name" content="KFT Fitness">
  <meta name="theme-color" content="#3D5AFE">
  <meta name="msapplication-TileColor" content="#3D5AFE">

  <!-- Icons -->
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" sizes="192x192" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" href="favicon.png">

  <title>KFT Fitness - Personal Training App</title>
  <link rel="manifest" href="manifest.json">

  <!-- ⚡ INSTANT LOADING OPTIMIZATIONS ⚡ -->

  <!-- Critical Resource Preloading with High Priority -->
  <link rel="preload" href="flutter_bootstrap.js" as="script" crossorigin>
  <link rel="preload" href="flutter.js" as="script" crossorigin>
  <link rel="preload" href="main.dart.js" as="script" crossorigin>

  <!-- Preconnect to critical domains for instant connection -->
  <link rel="preconnect" href="//player.vimeo.com" crossorigin>
  <link rel="preconnect" href="//vimeo.com" crossorigin>
  <link rel="preconnect" href="//fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="//fonts.gstatic.com" crossorigin>

  <!-- DNS prefetch for additional domains -->
  <link rel="dns-prefetch" href="//i.vimeocdn.com">
  <link rel="dns-prefetch" href="//f.vimeocdn.com">

  <!-- Preload critical fonts with high priority -->
  <link rel="preload" href="assets/fonts/MaterialIcons-Regular.otf" as="font" type="font/otf" crossorigin>
  <link rel="preload" href="assets/packages/cupertino_icons/assets/CupertinoIcons.ttf" as="font" type="font/ttf" crossorigin>

  <!-- Preload essential manifests -->
  <link rel="preload" href="assets/AssetManifest.json" as="fetch" crossorigin>
  <link rel="preload" href="assets/FontManifest.json" as="fetch" crossorigin>

  <!-- Preload app logo for instant display -->
  <link rel="preload" href="assets/assets/images/logo.webp" as="image" crossorigin>

  <!-- Preload critical icons -->
  <link rel="preload" href="icons/Icon-192.png" as="image">
  <link rel="preload" href="favicon.png" as="image">

  <!-- Critical CSS for instant rendering -->
  <style>
    /* Reset and base styles for instant rendering */
    *, *::before, *::after { box-sizing: border-box; }
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      background: #3D5AFE;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
    }

    /* Instant splash screen */
    #instant-splash {
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      width: 100vw; height: 100vh;
      background: linear-gradient(135deg, #3D5AFE 0%, #536DFE 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      opacity: 1;
      transition: opacity 0.3s ease-out;
    }

    /* App logo */
    .app-logo {
      width: 80px;
      height: 80px;
      background: rgba(255,255,255,0.1);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24px;
      animation: logoFloat 2s ease-in-out infinite;
    }

    .app-logo::before {
      content: '🥋';
      font-size: 40px;
    }

    /* App title */
    .app-title {
      color: white;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      opacity: 0.95;
    }

    .app-subtitle {
      color: rgba(255,255,255,0.8);
      font-size: 14px;
      margin-bottom: 32px;
    }

    /* Modern loading spinner */
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255,255,255,0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Loading text */
    .loading-text {
      color: rgba(255,255,255,0.9);
      font-size: 13px;
      margin-top: 16px;
      animation: pulse 1.5s ease-in-out infinite;
    }

    /* Animations */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes logoFloat {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }

    /* Hide splash when Flutter loads */
    .flutter-loaded #instant-splash {
      opacity: 0;
      pointer-events: none;
    }

    /* Flutter app container */
    #flutter-app {
      opacity: 0;
      transition: opacity 0.3s ease-in;
    }

    .flutter-loaded #flutter-app {
      opacity: 1;
    }
  </style>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
</head>
<body>
  <!-- ⚡ Instant Loading Splash Screen ⚡ -->
  <div id="instant-splash">
    <div class="app-logo"></div>
    <div class="app-title">KFT Fitness</div>
    <div class="app-subtitle">Personal Training App</div>
    <div class="loading-spinner"></div>
    <div class="loading-text">Loading your workout...</div>
  </div>

  <!-- Flutter App Container -->
  <div id="flutter-app"></div>

  <!-- ⚡ Instant Loading Script ⚡ -->
  <script>
    // Prevent default install prompts
    window.addEventListener('beforeinstallprompt', function(e) {
      e.preventDefault();
      return false;
    });

    // Performance optimization: Start loading Flutter immediately
    const startTime = performance.now();

    // Preload critical resources
    function preloadCriticalResources() {
      const criticalResources = [
        'flutter.js',
        'main.dart.js'
      ];

      criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = resource;
        document.head.appendChild(link);
      });
    }

    // Hide splash screen when Flutter is ready
    function hideSplashScreen() {
      const loadTime = performance.now() - startTime;
      console.log(`⚡ App loaded in ${Math.round(loadTime)}ms`);

      document.body.classList.add('flutter-loaded');

      // Remove splash screen after animation
      setTimeout(() => {
        const splash = document.getElementById('instant-splash');
        if (splash) {
          splash.remove();
        }
      }, 300);
    }

    // Start preloading immediately
    preloadCriticalResources();

    // Listen for Flutter ready event
    window.addEventListener('flutter-first-frame', hideSplashScreen);

    // Fallback: Hide splash after maximum wait time
    setTimeout(hideSplashScreen, 3000);
  </script>

  <!-- Load Flutter Bootstrap with high priority -->
  <script src="flutter_bootstrap.js" async></script>

  <!-- PWA Service Worker Registration -->
  <script>
    // Register service worker for PWA functionality
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('✅ Service Worker registered successfully:', registration.scope);

            // Check for updates
            registration.addEventListener('updatefound', () => {
              console.log('🔄 Service Worker update found');
            });
          })
          .catch((error) => {
            console.error('❌ Service Worker registration failed:', error);
          });
      });
    } else {
      console.warn('⚠️ Service Worker not supported in this browser');
    }
  </script>

</body></html>