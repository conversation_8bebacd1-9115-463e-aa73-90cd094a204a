<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting to KFT Fitness App...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Loading KFT Fitness App...</h2>
        <p>You will be redirected automatically.</p>
        <p><a href="#" id="manual-link" style="color: #fff; text-decoration: underline;">Click here if not redirected</a></p>
    </div>

    <script>
        // Get secure token from URL
        const urlParams = new URLSearchParams(window.location.search);
        const secureToken = urlParams.get('secure_token');
        
        // Build Flutter app URL
        let flutterUrl = '/app/';
        if (secureToken) {
            flutterUrl += '?secure_token=' + encodeURIComponent(secureToken);
        }
        
        // Update manual link
        document.getElementById('manual-link').href = flutterUrl;
        
        // Redirect after 2 seconds
        setTimeout(function() {
            window.location.href = flutterUrl;
        }, 2000);
    </script>
</body>
</html>
