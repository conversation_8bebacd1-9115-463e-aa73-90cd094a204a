package com.example.kft

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.content.pm.ActivityInfo
import android.view.WindowManager
import android.os.Build
import android.webkit.WebSettings
import android.content.Intent
import android.net.Uri
import android.os.Bundle

class MainActivity : FlutterActivity() {
    private val ORIENTATION_CHANNEL = "com.example.kft/orientation"
    private val SECURE_TOKEN_CHANNEL = "com.kft.fitness/secure_token"
    private var secureTokenChannel: MethodChannel? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Register performance monitoring plugins
        flutterEngine.plugins.add(DeviceInfoPlugin())
        flutterEngine.plugins.add(PerformancePlugin())

        // Configure WebView settings for better video support
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            WebSettings.getDefaultUserAgent(this)
        }

        // Set up secure token method channel
        secureTokenChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SECURE_TOKEN_CHANNEL)
        secureTokenChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "getInitialLink" -> {
                    val initialLink = getInitialLink()
                    result.success(initialLink)
                }
                else -> result.notImplemented()
            }
        }

        // Set up orientation method channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, ORIENTATION_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "setLandscape" -> {
                    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                    // Enable fullscreen for landscape mode
                    window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    result.success(null)
                }
                "setPortrait" -> {
                    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                    // Remove fullscreen flags for portrait mode
                    window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    result.success(null)
                }
                "setAuto" -> {
                    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                    // Remove fullscreen flags
                    window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    result.success(null)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        if (intent?.action == Intent.ACTION_VIEW) {
            val data: Uri? = intent.data
            data?.let { uri ->
                val secureToken = uri.getQueryParameter("secure_token")
                if (secureToken != null) {
                    // Send secure token to Flutter
                    secureTokenChannel?.invokeMethod("handleSecureToken", mapOf(
                        "secure_token" to secureToken
                    ))
                }
            }
        }
    }

    private fun getInitialLink(): String? {
        return intent?.data?.toString()
    }
}
