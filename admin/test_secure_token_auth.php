<?php
/**
 * Test script for secure token authentication API
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Start session for admin authentication
session_start();

// Initialize auth
$auth = new Auth();

// Check if admin is logged in
if (!$auth->isLoggedIn()) {
    echo "<h1>❌ Admin Authentication Required</h1>";
    echo "<p>Please <a href='login.php'>login as admin</a> to run this test.</p>";
    exit;
}

echo "<h1>🔐 Secure Token Authentication API Test</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Find a test user (first active user)
    $userResult = $conn->query("SELECT id, username, name FROM users WHERE is_active = 1 LIMIT 1");
    
    if ($userResult->num_rows === 0) {
        echo "<h2>❌ No Test User Found</h2>";
        echo "<p>Please create at least one active user to test with.</p>";
        exit;
    }

    $testUser = $userResult->fetch_assoc();
    echo "<h2>✅ Test User Found</h2>";
    echo "<p><strong>User:</strong> {$testUser['name']} (ID: {$testUser['id']}, Username: {$testUser['username']})</p>";

    // Step 1: Generate a secure login token
    echo "<h2>Step 1: Generate Secure Login Token</h2>";
    
    $token = bin2hex(random_bytes(64)); // 128 character token
    $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60)); // 24 hours expiry
    $adminId = $auth->getUserId();
    
    // Insert token into database
    $insertQuery = "INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'test';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'test-script';
    $stmt->bind_param("isisss", $testUser['id'], $token, $adminId, $expiresAt, $ipAddress, $userAgent);
    
    if ($stmt->execute()) {
        echo "<p>✅ Secure token generated successfully</p>";
        echo "<p><strong>Token:</strong> " . substr($token, 0, 20) . "... (128 chars)</p>";
        echo "<p><strong>Expires:</strong> $expiresAt</p>";
    } else {
        echo "<p>❌ Failed to generate secure token: " . $stmt->error . "</p>";
        exit;
    }
    $stmt->close();

    // Step 2: Test the secure token authentication API
    echo "<h2>Step 2: Test Secure Token Authentication API</h2>";
    
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/secure_token_auth.php';
    
    $postData = json_encode([
        'secure_token' => $token
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $response = file_get_contents($apiUrl, false, $context);
    
    if ($response === false) {
        echo "<p>❌ Failed to call API endpoint</p>";
        echo "<p><strong>API URL:</strong> $apiUrl</p>";
    } else {
        $responseData = json_decode($response, true);
        
        if ($responseData && $responseData['success'] === true) {
            echo "<p>✅ API authentication successful</p>";
            echo "<p><strong>JWT Token:</strong> " . substr($responseData['token'], 0, 50) . "...</p>";
            echo "<p><strong>User ID:</strong> " . $responseData['user']['id'] . "</p>";
            echo "<p><strong>User Name:</strong> " . $responseData['user']['name'] . "</p>";
            echo "<p><strong>Session Type:</strong> " . $responseData['session_info']['session_type'] . "</p>";
        } else {
            echo "<p>❌ API authentication failed</p>";
            echo "<p><strong>Error:</strong> " . ($responseData['error'] ?? 'Unknown error') . "</p>";
            echo "<p><strong>Response:</strong> " . htmlspecialchars($response) . "</p>";
        }
    }

    // Step 3: Generate Flutter app URL
    echo "<h2>Step 3: Flutter App URL</h2>";
    
    $baseUrl = 'http://' . $_SERVER['HTTP_HOST'];
    $flutterUrl = $baseUrl . "/?secure_token=" . urlencode($token);
    
    echo "<p>✅ Flutter app URL generated</p>";
    echo "<p><strong>URL:</strong> <a href='$flutterUrl' target='_blank'>$flutterUrl</a></p>";
    echo "<p><em>Click the link above to test the complete flow in the Flutter app</em></p>";

    // Step 4: Check token status
    echo "<h2>Step 4: Token Status Check</h2>";
    
    $checkQuery = "SELECT is_used, used_at FROM secure_login_tokens WHERE token = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    $tokenStatus = $result->fetch_assoc();
    $stmt->close();
    
    if ($tokenStatus) {
        echo "<p><strong>Token Used:</strong> " . ($tokenStatus['is_used'] ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>Used At:</strong> " . ($tokenStatus['used_at'] ?? 'Not used yet') . "</p>";
    }

    echo "<h2>🎯 Test Instructions</h2>";
    echo "<ol>";
    echo "<li>Click the Flutter app URL above</li>";
    echo "<li>The app should automatically authenticate and load the user's profile</li>";
    echo "<li>You should NOT see the login screen</li>";
    echo "<li>The URL should be cleaned (secure_token parameter removed)</li>";
    echo "<li>Refresh this page to check if the token was marked as used</li>";
    echo "</ol>";

} catch (Exception $e) {
    echo "<h2>❌ Test Failed</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
