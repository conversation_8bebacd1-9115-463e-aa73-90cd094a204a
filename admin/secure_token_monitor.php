<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Check if admin is logged in
$auth = new Auth();
if (!$auth->isLoggedIn() || !$auth->hasRole(['admin', 'staff'])) {
    Utilities::redirect('login.php');
}

$db = new Database();
$conn = $db->getConnection();

// Get statistics
$stats = [];

// Total tokens generated
$result = $conn->query("SELECT COUNT(*) as total FROM secure_login_tokens");
$stats['total_tokens'] = $result->fetch_assoc()['total'];

// Active tokens (not used and not expired)
$result = $conn->query("SELECT COUNT(*) as active FROM secure_login_tokens WHERE is_used = FALSE AND expires_at > NOW()");
$stats['active_tokens'] = $result->fetch_assoc()['active'];

// Used tokens
$result = $conn->query("SELECT COUNT(*) as used FROM secure_login_tokens WHERE is_used = TRUE");
$stats['used_tokens'] = $result->fetch_assoc()['used'];

// Expired tokens
$result = $conn->query("SELECT COUNT(*) as expired FROM secure_login_tokens WHERE expires_at <= NOW()");
$stats['expired_tokens'] = $result->fetch_assoc()['expired'];

// Recent activity (last 24 hours)
$result = $conn->query("SELECT COUNT(*) as recent FROM secure_login_tokens WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
$stats['recent_activity'] = $result->fetch_assoc()['recent'];

// Get recent tokens
$recentTokens = [];
$result = $conn->query("
    SELECT slt.*, u.name, u.username, u.phone_number, au.username as admin_username
    FROM secure_login_tokens slt
    JOIN users u ON slt.user_id = u.id
    LEFT JOIN admin_users au ON slt.generated_by_admin = au.id
    ORDER BY slt.created_at DESC
    LIMIT 20
");
while ($row = $result->fetch_assoc()) {
    $recentTokens[] = $row;
}

// Get recent admin actions related to secure tokens
$recentActions = [];
$result = $conn->query("
    SELECT aal.*, au.username as admin_username
    FROM admin_action_logs aal
    LEFT JOIN admin_users au ON aal.admin_id = au.id
    WHERE aal.action LIKE '%secure%token%' OR aal.action LIKE '%secure_login%'
    ORDER BY aal.created_at DESC
    LIMIT 10
");
while ($row = $result->fetch_assoc()) {
    $recentActions[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Secure Token Monitor - KFT Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
        .token-row {
            border-left: 4px solid #dee2e6;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 0 5px 5px 0;
        }
        .token-row.active {
            border-left-color: #28a745;
        }
        .token-row.used {
            border-left-color: #6c757d;
        }
        .token-row.expired {
            border-left-color: #dc3545;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-shield-alt"></i> Secure Token Monitor</h1>
                    <div>
                        <button class="btn btn-outline-primary" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?php echo $stats['total_tokens']; ?></div>
                    <div>Total Tokens</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?php echo $stats['active_tokens']; ?></div>
                    <div>Active Tokens</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?php echo $stats['used_tokens']; ?></div>
                    <div>Used Tokens</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?php echo $stats['expired_tokens']; ?></div>
                    <div>Expired Tokens</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?php echo $stats['recent_activity']; ?></div>
                    <div>Last 24h</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-number"><?php echo round(($stats['used_tokens'] / max($stats['total_tokens'], 1)) * 100); ?>%</div>
                    <div>Usage Rate</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Tokens -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> Recent Secure Tokens</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentTokens)): ?>
                            <p class="text-muted">No secure tokens found.</p>
                        <?php else: ?>
                            <?php foreach ($recentTokens as $token): ?>
                                <?php
                                $status = 'expired';
                                $statusText = 'Expired';
                                $statusIcon = 'fas fa-clock';
                                
                                if ($token['is_used']) {
                                    $status = 'used';
                                    $statusText = 'Used';
                                    $statusIcon = 'fas fa-check-circle';
                                } elseif (strtotime($token['expires_at']) > time()) {
                                    $status = 'active';
                                    $statusText = 'Active';
                                    $statusIcon = 'fas fa-play-circle';
                                }
                                ?>
                                <div class="token-row <?php echo $status; ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-user"></i> <?php echo htmlspecialchars($token['name']); ?>
                                                <small class="text-muted">(<?php echo htmlspecialchars($token['username']); ?>)</small>
                                            </h6>
                                            <p class="mb-1">
                                                <small>
                                                    <i class="fas fa-key"></i> Token: <?php echo substr($token['token'], 0, 20); ?>...
                                                    <br>
                                                    <i class="fas fa-user-shield"></i> Generated by: <?php echo htmlspecialchars($token['admin_username'] ?? 'Unknown'); ?>
                                                    <br>
                                                    <i class="fas fa-calendar"></i> Created: <?php echo date('M j, Y g:i A', strtotime($token['created_at'])); ?>
                                                    <br>
                                                    <i class="fas fa-clock"></i> Expires: <?php echo date('M j, Y g:i A', strtotime($token['expires_at'])); ?>
                                                    <?php if ($token['used_at']): ?>
                                                        <br>
                                                        <i class="fas fa-check"></i> Used: <?php echo date('M j, Y g:i A', strtotime($token['used_at'])); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </p>
                                        </div>
                                        <span class="badge bg-<?php echo $status === 'active' ? 'success' : ($status === 'used' ? 'secondary' : 'danger'); ?> status-badge">
                                            <i class="<?php echo $statusIcon; ?>"></i> <?php echo $statusText; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Admin Actions -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Recent Actions</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentActions)): ?>
                            <p class="text-muted">No recent actions found.</p>
                        <?php else: ?>
                            <?php foreach ($recentActions as $action): ?>
                                <div class="mb-3 p-2 border-start border-primary">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($action['action']); ?></h6>
                                    <small class="text-muted">
                                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($action['admin_username'] ?? 'System'); ?>
                                        <br>
                                        <i class="fas fa-clock"></i> <?php echo date('M j, g:i A', strtotime($action['created_at'])); ?>
                                        <?php if ($action['details']): ?>
                                            <br>
                                            <i class="fas fa-info-circle"></i> <?php echo htmlspecialchars(substr($action['details'], 0, 100)); ?>...
                                        <?php endif; ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="btn btn-primary refresh-btn" onclick="location.reload()" title="Refresh Data">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
