<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Get the secure token from URL parameter
$secureToken = isset($_GET['secure_token']) ? $_GET['secure_token'] : null;

if (!$secureToken) {
    // No token provided, redirect to normal login
    header('Location: login.php');
    exit;
}

// Detect if this is a mobile device or if the Flutter app is available
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$isMobile = preg_match('/Mobile|Android|iPhone|iPad/', $userAgent);

// Try to redirect to Flutter app first (for mobile devices)
if ($isMobile) {
    // Create Flutter app deep link
    $appDeepLink = "kftfitness://login?secure_token=" . urlencode($secureToken);
    
    // Create a page that attempts to open the app and falls back to web
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Opening KFT Fitness App...</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
            }
            .container {
                max-width: 400px;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            .logo {
                font-size: 2.5em;
                margin-bottom: 20px;
                font-weight: bold;
            }
            .message {
                font-size: 1.1em;
                margin-bottom: 30px;
                line-height: 1.5;
            }
            .button {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                color: white;
                padding: 15px 30px;
                border-radius: 50px;
                text-decoration: none;
                font-size: 1em;
                font-weight: 600;
                display: inline-block;
                margin: 10px;
                transition: all 0.3s ease;
            }
            .button:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }
            .spinner {
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-top: 3px solid white;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">🏋️ KFT Fitness</div>
            <div class="message">
                <div class="spinner"></div>
                <div id="status-message">Opening your secure login in the KFT Fitness app...</div>
            </div>
            <div id="fallback" style="display: none;">
                <p>Having trouble opening the app?</p>
                <a href="<?php echo htmlspecialchars($appDeepLink); ?>" class="button">🚀 Try Opening App Again</a>
                <a href="login.php?secure_token=<?php echo urlencode($secureToken); ?>" class="button">💻 Use Web Login Instead</a>
                <div style="margin-top: 20px; font-size: 0.9em; opacity: 0.8;">
                    <p>💡 <strong>Tip:</strong> Make sure you have the KFT Fitness app installed on your device.</p>
                </div>
            </div>
        </div>

        <script>
            let attemptCount = 0;
            const maxAttempts = 3;

            function updateStatus(message) {
                document.getElementById('status-message').textContent = message;
            }

            function tryOpenApp() {
                attemptCount++;
                updateStatus(`Attempting to open app (${attemptCount}/${maxAttempts})...`);

                // Try to open the app
                window.location.href = '<?php echo $appDeepLink; ?>';

                // Also try using a hidden iframe (alternative method)
                setTimeout(function() {
                    var iframe = document.createElement('iframe');
                    iframe.style.display = 'none';
                    iframe.src = '<?php echo $appDeepLink; ?>';
                    document.body.appendChild(iframe);

                    // Remove iframe after a short delay
                    setTimeout(function() {
                        if (iframe.parentNode) {
                            iframe.parentNode.removeChild(iframe);
                        }
                    }, 1000);
                }, 500);
            }

            // Try to open the app immediately
            tryOpenApp();

            // Show fallback options after 3 seconds
            setTimeout(function() {
                if (attemptCount < maxAttempts) {
                    updateStatus('Trying alternative method...');
                    tryOpenApp();
                } else {
                    document.getElementById('fallback').style.display = 'block';
                    document.querySelector('.spinner').style.display = 'none';
                    updateStatus('Need help opening the app?');
                }
            }, 3000);

            // Final fallback after 6 seconds
            setTimeout(function() {
                document.getElementById('fallback').style.display = 'block';
                document.querySelector('.spinner').style.display = 'none';
                updateStatus('Choose an option below:');
            }, 6000);
        </script>
    </body>
    </html>
    <?php
} else {
    // Desktop/non-mobile device - redirect to web login
    header('Location: login.php?secure_token=' . urlencode($secureToken));
    exit;
}
?>
