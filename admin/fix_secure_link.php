<?php
/**
 * Fix secure login link for user ID 83
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';
require_once 'includes/url_shortener.php';

// Start session for admin authentication
session_start();

// Initialize auth
$auth = new Auth();

// Check if admin is logged in
if (!$auth->isLoggedIn()) {
    echo "<h1>❌ Admin Authentication Required</h1>";
    echo "<p>Please <a href='login.php'>login as admin</a> to run this fix.</p>";
    exit;
}

echo "<h1>🔧 Fix Secure Login Link for User ID 83</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    $userId = 83;

    // Get user info
    $userQuery = "SELECT id, username, name FROM users WHERE id = ?";
    $stmt = $conn->prepare($userQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo "<p>❌ User ID $userId not found</p>";
        exit;
    }
    
    $user = $result->fetch_assoc();
    echo "<h2>User: {$user['name']} (ID: {$user['id']}, Username: {$user['username']})</h2>";

    // Step 1: Revoke any existing active tokens for this user
    echo "<h3>Step 1: Revoking existing tokens</h3>";
    $revokeQuery = "UPDATE secure_login_tokens SET is_used = TRUE, used_at = NOW() WHERE user_id = ? AND is_used = FALSE";
    $stmt = $conn->prepare($revokeQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    echo "<p>✅ Revoked " . $stmt->affected_rows . " existing tokens</p>";

    // Step 2: Deactivate any existing short URLs for this user
    echo "<h3>Step 2: Deactivating existing short URLs</h3>";
    $deactivateQuery = "UPDATE short_urls SET is_active = 0 WHERE user_id = ? AND is_active = 1";
    $stmt = $conn->prepare($deactivateQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    echo "<p>✅ Deactivated " . $stmt->affected_rows . " existing short URLs</p>";

    // Step 3: Generate new secure token
    echo "<h3>Step 3: Generating new secure token</h3>";
    $token = bin2hex(random_bytes(64)); // 128 character token
    $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60)); // 24 hours expiry
    $adminId = $auth->getUserId();
    
    // Insert token into database
    $insertQuery = "INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'admin-fix';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'admin-fix-script';
    $stmt->bind_param("isisss", $userId, $token, $adminId, $expiresAt, $ipAddress, $userAgent);
    
    if ($stmt->execute()) {
        echo "<p>✅ New secure token generated</p>";
        echo "<p><strong>Token:</strong> " . substr($token, 0, 20) . "... (128 chars)</p>";
        echo "<p><strong>Expires:</strong> $expiresAt</p>";
    } else {
        echo "<p>❌ Failed to generate token: " . $stmt->error . "</p>";
        exit;
    }

    // Step 4: Generate correct Flutter app URL
    echo "<h3>Step 4: Generating Flutter app URL</h3>";
    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
    $longUrl = $baseUrl . "/?secure_token=" . urlencode($token);
    
    echo "<p><strong>Long URL:</strong> " . htmlspecialchars($longUrl) . "</p>";

    // Step 5: Create new short URL
    echo "<h3>Step 5: Creating new short URL</h3>";
    $urlShortener = new UrlShortener($conn);
    $shortUrlResult = $urlShortener->createShortUrl($longUrl, $userId, $expiresAt);

    if ($shortUrlResult['success']) {
        echo "<p>✅ New short URL created</p>";
        echo "<p><strong>Short Code:</strong> " . htmlspecialchars($shortUrlResult['short_code']) . "</p>";
        echo "<p><strong>Short URL:</strong> <a href='" . htmlspecialchars($shortUrlResult['short_url']) . "' target='_blank'>" . htmlspecialchars($shortUrlResult['short_url']) . "</a></p>";
        
        $finalUrl = $shortUrlResult['short_url'];
    } else {
        echo "<p>⚠️ Short URL creation failed, using long URL</p>";
        echo "<p>Error: " . htmlspecialchars($shortUrlResult['error']) . "</p>";
        $finalUrl = $longUrl;
    }

    // Step 6: Test the URLs
    echo "<h3>Step 6: Test URLs</h3>";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>🎯 New Secure Login Link for User {$user['name']}</h4>";
    echo "<p><strong>Short URL:</strong> <a href='" . htmlspecialchars($finalUrl) . "' target='_blank'>" . htmlspecialchars($finalUrl) . "</a></p>";
    echo "<p><strong>Long URL:</strong> <a href='" . htmlspecialchars($longUrl) . "' target='_blank'>" . htmlspecialchars($longUrl) . "</a></p>";
    echo "<p><em>Click either link to test the Flutter app authentication</em></p>";
    echo "</div>";

    // Step 7: Instructions
    echo "<h3>Step 7: Testing Instructions</h3>";
    echo "<ol>";
    echo "<li>Click the short URL above</li>";
    echo "<li>It should redirect to the Flutter app at the root domain</li>";
    echo "<li>The Flutter app should automatically authenticate the user</li>";
    echo "<li>You should see the user's profile without a login screen</li>";
    echo "<li>The URL should be cleaned (secure_token parameter removed)</li>";
    echo "</ol>";

} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 5px 0; }
ol li { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
