<?php
/**
 * Debug script to check secure URL generation
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';
require_once 'includes/url_shortener.php';

// Start session for admin authentication
session_start();

// Initialize auth
$auth = new Auth();

// Check if admin is logged in
if (!$auth->isLoggedIn()) {
    echo "<h1>❌ Admin Authentication Required</h1>";
    echo "<p>Please <a href='login.php'>login as admin</a> to run this debug.</p>";
    exit;
}

echo "<h1>🔍 Secure URL Debug</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Check the specific short URL
    $shortCode = 'gzn1tv';
    
    echo "<h2>Checking Short URL: $shortCode</h2>";
    
    $query = "SELECT * FROM short_urls WHERE short_code = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $shortCode);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "<h3>✅ Short URL Found</h3>";
        echo "<p><strong>Short Code:</strong> " . htmlspecialchars($row['short_code']) . "</p>";
        echo "<p><strong>Long URL:</strong> " . htmlspecialchars($row['long_url']) . "</p>";
        echo "<p><strong>User ID:</strong> " . htmlspecialchars($row['user_id']) . "</p>";
        echo "<p><strong>Expires At:</strong> " . htmlspecialchars($row['expires_at']) . "</p>";
        echo "<p><strong>Is Active:</strong> " . ($row['is_active'] ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>Click Count:</strong> " . htmlspecialchars($row['click_count']) . "</p>";
        echo "<p><strong>Created At:</strong> " . htmlspecialchars($row['created_at']) . "</p>";
        
        // Check if the long URL is correct
        $longUrl = $row['long_url'];
        echo "<h3>Analyzing Long URL</h3>";
        
        if (strpos($longUrl, 'secure_token=') !== false) {
            echo "<p>✅ Contains secure_token parameter</p>";
            
            // Extract the token
            $urlParts = parse_url($longUrl);
            parse_str($urlParts['query'], $queryParams);
            $token = $queryParams['secure_token'] ?? null;
            
            if ($token) {
                echo "<p><strong>Token:</strong> " . substr($token, 0, 20) . "... (128 chars)</p>";
                
                // Check if token exists in secure_login_tokens table
                $tokenQuery = "SELECT * FROM secure_login_tokens WHERE token = ?";
                $tokenStmt = $conn->prepare($tokenQuery);
                $tokenStmt->bind_param("s", $token);
                $tokenStmt->execute();
                $tokenResult = $tokenStmt->get_result();
                
                if ($tokenResult->num_rows > 0) {
                    $tokenRow = $tokenResult->fetch_assoc();
                    echo "<p>✅ Token found in database</p>";
                    echo "<p><strong>Token User ID:</strong> " . htmlspecialchars($tokenRow['user_id']) . "</p>";
                    echo "<p><strong>Token Expires:</strong> " . htmlspecialchars($tokenRow['expires_at']) . "</p>";
                    echo "<p><strong>Token Used:</strong> " . ($tokenRow['is_used'] ? 'Yes' : 'No') . "</p>";
                } else {
                    echo "<p>❌ Token not found in database</p>";
                }
            }
        } else {
            echo "<p>❌ Does not contain secure_token parameter</p>";
        }
        
        // Check what the URL should be
        echo "<h3>What the URL Should Be</h3>";
        $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
        $correctUrl = $baseUrl . "/?secure_token=" . ($token ?? 'TOKEN_HERE');
        echo "<p><strong>Correct Flutter URL:</strong> " . htmlspecialchars($correctUrl) . "</p>";
        
        if ($longUrl !== $correctUrl && isset($token)) {
            echo "<p>❌ <strong>PROBLEM FOUND:</strong> Long URL is incorrect!</p>";
            echo "<p><strong>Current:</strong> " . htmlspecialchars($longUrl) . "</p>";
            echo "<p><strong>Should be:</strong> " . htmlspecialchars($correctUrl) . "</p>";
            
            // Offer to fix it
            echo "<h3>Fix the URL?</h3>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='short_code' value='" . htmlspecialchars($shortCode) . "'>";
            echo "<input type='hidden' name='correct_url' value='" . htmlspecialchars($correctUrl) . "'>";
            echo "<button type='submit' name='fix_url' class='btn btn-primary'>Fix URL</button>";
            echo "</form>";
        }
        
    } else {
        echo "<h3>❌ Short URL Not Found</h3>";
        echo "<p>The short code '$shortCode' does not exist in the database.</p>";
    }
    
    // Handle URL fix
    if (isset($_POST['fix_url']) && isset($_POST['short_code']) && isset($_POST['correct_url'])) {
        $updateQuery = "UPDATE short_urls SET long_url = ? WHERE short_code = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param("ss", $_POST['correct_url'], $_POST['short_code']);
        
        if ($updateStmt->execute()) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "✅ URL fixed successfully! The short URL now points to the Flutter app.";
            echo "</div>";
            echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "❌ Failed to fix URL: " . $updateStmt->error;
            echo "</div>";
        }
    }
    
    echo "<h2>Test the Short URL</h2>";
    $testUrl = "https://c9f3fdcffc0c.ngrok-free.app/admin/s/$shortCode";
    echo "<p><a href='$testUrl' target='_blank'>$testUrl</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 5px 0; }
.btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
.btn:hover { background: #0056b3; }
</style>
