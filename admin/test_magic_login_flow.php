<?php
/**
 * Test the complete magic login flow
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';
require_once 'includes/url_shortener.php';

// Start session for admin authentication
session_start();

// Initialize auth
$auth = new Auth();

// Check if admin is logged in
if (!$auth->isLoggedIn()) {
    echo "<h1>❌ Admin Authentication Required</h1>";
    echo "<p>Please <a href='login.php'>login as admin</a> to run this test.</p>";
    exit;
}

echo "<h1>🪄 Magic Login Flow Test</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    $userId = 83; // Test with user ID 83

    // Get user info
    $userQuery = "SELECT id, username, name FROM users WHERE id = ?";
    $stmt = $conn->prepare($userQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo "<p>❌ User ID $userId not found</p>";
        exit;
    }
    
    $user = $result->fetch_assoc();
    echo "<h2>Testing Magic Login for User: {$user['name']} (ID: {$user['id']}, Username: {$user['username']})</h2>";

    // Step 1: Clean up existing tokens
    echo "<h3>Step 1: Clean up existing tokens</h3>";
    $revokeQuery = "UPDATE secure_login_tokens SET is_used = TRUE, used_at = NOW() WHERE user_id = ? AND is_used = FALSE";
    $stmt = $conn->prepare($revokeQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    echo "<p>✅ Revoked " . $stmt->affected_rows . " existing tokens</p>";

    // Step 2: Generate fresh magic login token
    echo "<h3>Step 2: Generate Fresh Magic Login Token</h3>";
    $token = bin2hex(random_bytes(64)); // 128 character token
    $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60)); // 24 hours expiry
    $adminId = $auth->getUserId();
    
    $insertQuery = "INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'test';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'test-script';
    $stmt->bind_param("isisss", $userId, $token, $adminId, $expiresAt, $ipAddress, $userAgent);
    
    if ($stmt->execute()) {
        echo "<p>✅ Magic login token generated</p>";
        echo "<p><strong>Token:</strong> " . substr($token, 0, 20) . "... (128 chars)</p>";
        echo "<p><strong>Expires:</strong> $expiresAt</p>";
    } else {
        echo "<p>❌ Failed to generate token</p>";
        exit;
    }

    // Step 3: Generate magic login URLs
    echo "<h3>Step 3: Generate Magic Login URLs</h3>";
    $baseUrl = 'https://c9f3fdcffc0c.ngrok-free.app';
    
    // Direct Flutter app URL with hash route
    $directMagicUrl = $baseUrl . "/flutter-app/#/magic-login/" . urlencode($token);
    echo "<p><strong>Direct Magic URL:</strong> <a href='" . htmlspecialchars($directMagicUrl) . "' target='_blank'>" . htmlspecialchars($directMagicUrl) . "</a></p>";
    
    // Create short URL for the magic login
    $urlShortener = new UrlShortener($conn);
    $shortUrlResult = $urlShortener->createShortUrl($directMagicUrl, $userId, $expiresAt);

    if ($shortUrlResult['success']) {
        $shortMagicUrl = $shortUrlResult['short_url'];
        echo "<p><strong>Short Magic URL:</strong> <a href='" . htmlspecialchars($shortMagicUrl) . "' target='_blank'>" . htmlspecialchars($shortMagicUrl) . "</a></p>";
    } else {
        echo "<p>⚠️ Short URL creation failed: " . htmlspecialchars($shortUrlResult['error']) . "</p>";
        $shortMagicUrl = $directMagicUrl;
    }

    // Step 4: Test Magic Login API
    echo "<h3>Step 4: Test Magic Login API</h3>";
    $apiUrl = 'https://c9f3fdcffc0c.ngrok-free.app/admin/api/auth/magic-login.php?token=' . urlencode($token);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Content-Type: application/json',
        ]
    ]);
    
    $response = file_get_contents($apiUrl, false, $context);
    
    if ($response === false) {
        echo "<p>❌ Failed to call magic login API</p>";
    } else {
        $responseData = json_decode($response, true);
        
        if ($responseData && $responseData['success'] === true) {
            echo "<p>✅ Magic login API successful</p>";
            echo "<p><strong>JWT Token:</strong> " . substr($responseData['token'], 0, 50) . "...</p>";
            echo "<p><strong>User Name:</strong> " . $responseData['user']['name'] . "</p>";
            echo "<p><strong>Login Method:</strong> " . $responseData['session_info']['login_method'] . "</p>";
        } else {
            echo "<p>❌ Magic login API failed: " . ($responseData['error'] ?? 'Unknown error') . "</p>";
            echo "<p><strong>Response:</strong> " . htmlspecialchars($response) . "</p>";
        }
    }

    // Step 5: Test URLs
    echo "<h3>Step 5: Test the Magic Login Flow</h3>";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 20px; margin: 15px 0; border-radius: 8px;'>";
    echo "<h4>🎯 Magic Login Test URLs</h4>";
    echo "<p><strong>Short Magic URL:</strong> <a href='" . htmlspecialchars($shortMagicUrl) . "' target='_blank' style='color: #007bff; font-weight: bold;'>" . htmlspecialchars($shortMagicUrl) . "</a></p>";
    echo "<p><strong>Direct Magic URL:</strong> <a href='" . htmlspecialchars($directMagicUrl) . "' target='_blank' style='color: #007bff;'>" . htmlspecialchars($directMagicUrl) . "</a></p>";
    echo "</div>";

    // Step 6: Expected Flow
    echo "<h3>Step 6: Expected Magic Login Flow</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>✅ What Should Happen:</h4>";
    echo "<ol>";
    echo "<li><strong>Click the Magic Login URL</strong> - Opens Flutter app directly</li>";
    echo "<li><strong>Magic Login Page loads</strong> - Shows 'Authenticating...' with spinner</li>";
    echo "<li><strong>Token validation</strong> - Calls /api/auth/magic-login.php automatically</li>";
    echo "<li><strong>Success message</strong> - Shows 'Welcome back! Redirecting...'</li>";
    echo "<li><strong>Auto-redirect</strong> - Navigates to user dashboard/home</li>";
    echo "<li><strong>User authenticated</strong> - No manual login required</li>";
    echo "</ol>";
    echo "</div>";

    // Step 7: Benefits
    echo "<h3>Step 7: Magic Login Benefits</h3>";
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>🎉 Advantages:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Direct Flutter access</strong> - No admin interface exposure</li>";
    echo "<li>✅ <strong>Single-use tokens</strong> - Secure, one-time authentication</li>";
    echo "<li>✅ <strong>Skip login screen</strong> - Instant user experience</li>";
    echo "<li>✅ <strong>Persistent session</strong> - 300-day JWT tokens</li>";
    echo "<li>✅ <strong>Clean URLs</strong> - Professional appearance</li>";
    echo "<li>✅ <strong>Mobile-friendly</strong> - Works on all devices</li>";
    echo "</ul>";
    echo "</div>";

    // Step 8: Token status
    echo "<h3>Step 8: Token Status</h3>";
    $checkQuery = "SELECT is_used, used_at FROM secure_login_tokens WHERE token = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    $tokenStatus = $result->fetch_assoc();
    
    if ($tokenStatus) {
        echo "<p><strong>Token Used:</strong> " . ($tokenStatus['is_used'] ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>Used At:</strong> " . ($tokenStatus['used_at'] ?? 'Not used yet') . "</p>";
        echo "<p><em>Refresh this page after testing to see if the token was marked as used</em></p>";
    }

} catch (Exception $e) {
    echo "<h2>❌ Test Failed</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
h1 { color: #6f42c1; }
p { margin: 8px 0; }
ol li, ul li { margin: 8px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
.highlight { background: #ffeb3b; padding: 2px 4px; border-radius: 3px; }
</style>
