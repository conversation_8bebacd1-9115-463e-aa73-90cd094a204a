<?php
/**
 * Test the complete secure login flow
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';
require_once 'includes/url_shortener.php';

// Start session for admin authentication
session_start();

// Initialize auth
$auth = new Auth();

// Check if admin is logged in
if (!$auth->isLoggedIn()) {
    echo "<h1>❌ Admin Authentication Required</h1>";
    echo "<p>Please <a href='login.php'>login as admin</a> to run this test.</p>";
    exit;
}

echo "<h1>🧪 Complete Secure Login Flow Test</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    $userId = 83; // Test with user ID 83

    // Get user info
    $userQuery = "SELECT id, username, name FROM users WHERE id = ?";
    $stmt = $conn->prepare($userQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo "<p>❌ User ID $userId not found</p>";
        exit;
    }
    
    $user = $result->fetch_assoc();
    echo "<h2>Testing with User: {$user['name']} (ID: {$user['id']}, Username: {$user['username']})</h2>";

    // Step 1: Generate secure token
    echo "<h3>Step 1: Generate Secure Token</h3>";
    $token = bin2hex(random_bytes(64)); // 128 character token
    $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60)); // 24 hours expiry
    $adminId = $auth->getUserId();
    
    $insertQuery = "INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'test';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'test-script';
    $stmt->bind_param("isisss", $userId, $token, $adminId, $expiresAt, $ipAddress, $userAgent);
    
    if ($stmt->execute()) {
        echo "<p>✅ Secure token generated: " . substr($token, 0, 20) . "...</p>";
    } else {
        echo "<p>❌ Failed to generate token</p>";
        exit;
    }

    // Step 2: Test secure token authentication API
    echo "<h3>Step 2: Test Secure Token Authentication API</h3>";
    $apiUrl = 'https://c9f3fdcffc0c.ngrok-free.app/admin/api/secure_token_auth.php';
    
    $postData = json_encode(['secure_token' => $token]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $response = file_get_contents($apiUrl, false, $context);
    
    if ($response === false) {
        echo "<p>❌ Failed to call API endpoint</p>";
    } else {
        $responseData = json_decode($response, true);
        
        if ($responseData && $responseData['success'] === true) {
            echo "<p>✅ API authentication successful</p>";
            echo "<p><strong>JWT Token:</strong> " . substr($responseData['token'], 0, 50) . "...</p>";
        } else {
            echo "<p>❌ API authentication failed: " . ($responseData['error'] ?? 'Unknown error') . "</p>";
        }
    }

    // Step 3: Generate secure login URL
    echo "<h3>Step 3: Generate Secure Login URL</h3>";
    $baseUrl = 'https://c9f3fdcffc0c.ngrok-free.app';
    $longUrl = $baseUrl . "/admin/login.php?secure_token=" . urlencode($token);
    
    // Create short URL
    $urlShortener = new UrlShortener($conn);
    $shortUrlResult = $urlShortener->createShortUrl($longUrl, $userId, $expiresAt);

    if ($shortUrlResult['success']) {
        $finalUrl = $shortUrlResult['short_url'];
        echo "<p>✅ Short URL created: <a href='" . htmlspecialchars($finalUrl) . "' target='_blank'>" . htmlspecialchars($finalUrl) . "</a></p>";
    } else {
        $finalUrl = $longUrl;
        echo "<p>⚠️ Using long URL: <a href='" . htmlspecialchars($finalUrl) . "' target='_blank'>" . htmlspecialchars($finalUrl) . "</a></p>";
    }

    // Step 4: Test URLs
    echo "<h3>Step 4: Test the Complete Flow</h3>";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>🎯 Complete Test URLs</h4>";
    echo "<p><strong>Secure Login Link:</strong> <a href='" . htmlspecialchars($finalUrl) . "' target='_blank'>" . htmlspecialchars($finalUrl) . "</a></p>";
    echo "<p><strong>Direct Flutter App:</strong> <a href='https://c9f3fdcffc0c.ngrok-free.app/flutter-app/?secure_token=" . urlencode($token) . "' target='_blank'>Flutter App with Token</a></p>";
    echo "</div>";

    // Step 5: Expected Flow
    echo "<h3>Step 5: Expected Flow</h3>";
    echo "<ol>";
    echo "<li><strong>Click the Secure Login Link</strong> - Should redirect to admin/login.php</li>";
    echo "<li><strong>Admin login.php detects secure token</strong> - Validates token and creates session</li>";
    echo "<li><strong>Shows loading screen</strong> - 'Welcome to KFT Fitness!' message</li>";
    echo "<li><strong>Redirects to Flutter app</strong> - After 2 seconds, redirects to /flutter-app/</li>";
    echo "<li><strong>Flutter app loads</strong> - Should show the user's personalized content</li>";
    echo "<li><strong>URL is cleaned</strong> - secure_token parameter should be removed</li>";
    echo "</ol>";

    // Step 6: Troubleshooting
    echo "<h3>Step 6: Troubleshooting</h3>";
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>⚠️ If the flow doesn't work:</h4>";
    echo "<ul>";
    echo "<li>Check browser console for JavaScript errors</li>";
    echo "<li>Verify the Flutter app loads at: <a href='https://c9f3fdcffc0c.ngrok-free.app/flutter-app/' target='_blank'>https://c9f3fdcffc0c.ngrok-free.app/flutter-app/</a></li>";
    echo "<li>Test the API endpoint manually</li>";
    echo "<li>Check if the secure token is being processed by the Flutter app</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<h2>❌ Test Failed</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 5px 0; }
ol li, ul li { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
