<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Origin, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['phone_number'])) {
    $inputPhone = $_POST['phone_number'];
    $deviceId = isset($_POST['device_id']) ? $_POST['device_id'] : null;

    if (!empty($inputPhone)) {
        $db = new Database();
        $user = $db->getUserByPhone($inputPhone);

        if ($user) {
            // Device restriction logic
            if (!empty($user['device_id']) && $deviceId && $user['device_id'] !== $deviceId) {
                echo json_encode(['error' => 'DEVICE_ALREADY_REGISTERED']);
                exit;
            }
            echo json_encode([
                'exists' => true,
                'name' => $user['name'],
                'username' => $user['username']
            ]);
            exit;
        }
    }
}
echo json_encode(['exists' => false]); 