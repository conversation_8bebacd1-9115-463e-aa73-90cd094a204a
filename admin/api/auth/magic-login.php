<?php
/**
 * Magic Login API Endpoint
 * Validates magic login tokens and returns JWT tokens for Flutter app authentication
 * URL: /api/auth/magic-login?token=TOKEN
 */

// Set headers for JSON response and CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Allow both GET and POST requests
if (!in_array($_SERVER['REQUEST_METHOD'], ['GET', 'POST'])) {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/jwt.php';
require_once '../../includes/utilities.php';

// Function to return JSON response
function returnResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

// Function to log magic login authentication
function logMagicLoginAuth($conn, $tokenData, $success = true) {
    try {
        $logQuery = "INSERT INTO secure_login_logs (user_id, token_id, ip_address, user_agent, success, login_method, created_at) 
                     VALUES (?, ?, ?, ?, ?, 'magic_login', NOW())";
        $stmt = $conn->prepare($logQuery);
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $stmt->bind_param("iissi", $tokenData['user_id'], $tokenData['id'], $ipAddress, $userAgent, $success);
        $stmt->execute();
        $stmt->close();
    } catch (Exception $e) {
        error_log("Failed to log magic login auth: " . $e->getMessage());
    }
}

try {
    // Get token from query parameter or POST body
    $token = null;
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
    } else {
        $input = json_decode(file_get_contents('php://input'), true);
        $token = $input['token'] ?? $_POST['token'] ?? '';
    }

    // Validate token parameter
    if (empty($token)) {
        returnResponse(['success' => false, 'error' => 'Magic login token is required'], 400);
    }

    $token = trim($token);

    // Validate token format
    if (strlen($token) !== 128) {
        returnResponse(['success' => false, 'error' => 'Invalid magic login token format'], 400);
    }

    // Initialize database
    $db = new Database();
    $conn = $db->getConnection();

    // Check if token exists and is valid
    $query = "SELECT slt.*, u.id as user_id, u.username, u.name, u.email, u.phone_number, u.is_active, u.is_premium
              FROM secure_login_tokens slt
              JOIN users u ON slt.user_id = u.id
              WHERE slt.token = ? AND slt.is_used = FALSE AND slt.expires_at > NOW()";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        returnResponse(['success' => false, 'error' => 'Magic login link is invalid, expired, or already used'], 401);
    }

    $tokenData = $result->fetch_assoc();
    $stmt->close();

    // Check if user is active
    if (!$tokenData['is_active']) {
        returnResponse(['success' => false, 'error' => 'User account is inactive'], 401);
    }

    // Mark token as used (single-use)
    $updateQuery = "UPDATE secure_login_tokens SET is_used = TRUE, used_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("i", $tokenData['id']);
    $stmt->execute();
    $stmt->close();

    // Log the magic login authentication
    logMagicLoginAuth($conn, $tokenData, true);

    // Update user's last login time
    $updateLoginQuery = "UPDATE users SET last_login = NOW() WHERE id = ?";
    $stmt = $conn->prepare($updateLoginQuery);
    $stmt->bind_param("i", $tokenData['user_id']);
    $stmt->execute();
    $stmt->close();

    // Generate JWT token for Flutter app authentication
    $tokenExpiry = time() + (300 * 24 * 60 * 60); // 300 days for persistent login
    
    $jwtPayload = [
        'user_id' => $tokenData['user_id'],
        'username' => $tokenData['username'],
        'name' => $tokenData['name'],
        'phone_number' => $tokenData['phone_number'],
        'type' => 'access',
        'iat' => time(),
        'exp' => $tokenExpiry,
        'extended' => true,
        'magic_login' => true, // Flag to indicate this came from magic login
        'device_id' => 'magic_login_' . time() // Generate a device ID for magic login
    ];

    $jwtToken = generate_jwt($jwtPayload, APP_SECRET);

    // Generate refresh token
    $refreshTokenPayload = [
        'user_id' => $tokenData['user_id'],
        'type' => 'refresh',
        'iat' => time(),
        'exp' => time() + (365 * 24 * 60 * 60) // 1 year for refresh token
    ];

    $refreshToken = generate_jwt($refreshTokenPayload, APP_SECRET . '_refresh');

    // Store the new JWT token in the database for tracking
    $insertTokenQuery = "INSERT INTO api_tokens (user_id, token, device_id, expires_at, created_at) VALUES (?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($insertTokenQuery);
    $deviceId = $jwtPayload['device_id'];
    $expiresAt = date('Y-m-d H:i:s', $tokenExpiry);
    $stmt->bind_param("isss", $tokenData['user_id'], $jwtToken, $deviceId, $expiresAt);
    $stmt->execute();
    $stmt->close();

    // Return successful authentication response
    returnResponse([
        'success' => true,
        'message' => 'Magic login successful',
        'token' => $jwtToken,
        'refresh_token' => $refreshToken,
        'expires_at' => date('Y-m-d H:i:s', $tokenExpiry),
        'extended_session' => true,
        'magic_login' => true,
        'user' => [
            'id' => $tokenData['user_id'],
            'name' => $tokenData['name'],
            'username' => $tokenData['username'],
            'email' => $tokenData['email'],
            'phone_number' => $tokenData['phone_number'],
            'is_premium' => (bool)$tokenData['is_premium']
        ],
        'session_info' => [
            'device_id' => $deviceId,
            'login_time' => date('Y-m-d H:i:s'),
            'session_type' => 'magic_login',
            'login_method' => 'magic_token'
        ]
    ]);

} catch (Exception $e) {
    error_log("Magic login authentication error: " . $e->getMessage());
    returnResponse(['success' => false, 'error' => 'Internal server error during magic login'], 500);
}
?>
