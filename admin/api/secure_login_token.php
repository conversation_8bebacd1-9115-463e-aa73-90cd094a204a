<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/utilities.php';
require_once '../includes/url_shortener.php';

// Set JSON content type
header('Content-Type: application/json');

// Check if admin is logged in
$auth = new Auth();
if (!$auth->isLoggedIn() || !$auth->hasRole(['admin', 'staff'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid JSON input']);
    exit;
}

$action = $input['action'] ?? '';
$userId = intval($input['user_id'] ?? 0);

if (empty($action) || $userId <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

$db = new Database();
$conn = $db->getConnection();

try {
    switch ($action) {
        case 'generate':
            $result = generateSecureLoginToken($conn, $userId, $auth->getUserId());
            echo json_encode($result);
            break;
            
        case 'revoke':
            $result = revokeSecureLoginTokens($conn, $userId, $auth->getUserId());
            echo json_encode($result);
            break;
            
        case 'list':
            $result = listActiveTokens($conn, $userId);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
} catch (Exception $e) {
    error_log("Secure login token error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}

/**
 * Generate a secure single-use login token
 */
function generateSecureLoginToken($conn, $userId, $adminId) {
    // Verify user exists and is active
    $userQuery = "SELECT id, username, name, is_active FROM users WHERE id = ?";
    $stmt = $conn->prepare($userQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return ['success' => false, 'error' => 'User not found'];
    }
    
    $user = $result->fetch_assoc();
    if (!$user['is_active']) {
        return ['success' => false, 'error' => 'User account is inactive'];
    }
    
    // Revoke any existing active tokens for this user
    revokeSecureLoginTokens($conn, $userId, $adminId, false);
    
    // Generate secure token
    $token = bin2hex(random_bytes(64)); // 128 character token
    $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60)); // 24 hours expiry
    
    // Get client info
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    // Insert token into database
    $insertQuery = "INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $stmt->bind_param("isisss", $userId, $token, $adminId, $expiresAt, $ipAddress, $userAgent);
    
    if (!$stmt->execute()) {
        return ['success' => false, 'error' => 'Failed to generate token'];
    }
    
    // Log admin action
    logAdminAction($conn, $adminId, 'secure_login_token_generated', $userId, $user['username'], null, [
        'token_id' => $conn->insert_id,
        'expires_at' => $expiresAt,
        'ip_address' => $ipAddress
    ]);
    
    // Generate secure login URL pointing to Flutter app
    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];

    // Point to the Flutter web app root with secure token parameter
    $longUrl = $baseUrl . "/?secure_token=" . urlencode($token);

    // Create short URL
    $urlShortener = new UrlShortener($conn);
    $shortUrlResult = $urlShortener->createShortUrl($longUrl, $userId, $expiresAt);

    $finalUrl = $shortUrlResult['success'] ? $shortUrlResult['short_url'] : $longUrl;

    return [
        'success' => true,
        'token' => $token,
        'login_url' => $finalUrl,
        'long_url' => $longUrl,
        'short_url' => $shortUrlResult['success'] ? $shortUrlResult['short_url'] : null,
        'short_code' => $shortUrlResult['success'] ? $shortUrlResult['short_code'] : null,
        'expires_at' => $expiresAt,
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'name' => $user['name']
        ]
    ];
}

/**
 * Revoke all active secure login tokens for a user
 */
function revokeSecureLoginTokens($conn, $userId, $adminId, $returnResponse = true) {
    $updateQuery = "UPDATE secure_login_tokens SET is_used = TRUE, used_at = NOW() WHERE user_id = ? AND is_used = FALSE AND expires_at > NOW()";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    
    $revokedCount = $stmt->affected_rows;
    
    if ($revokedCount > 0) {
        // Get user info for logging
        $userQuery = "SELECT username FROM users WHERE id = ?";
        $stmt = $conn->prepare($userQuery);
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        
        // Log admin action
        logAdminAction($conn, $adminId, 'secure_login_tokens_revoked', $userId, $user['username'], null, [
            'revoked_count' => $revokedCount
        ]);
    }
    
    if (!$returnResponse) {
        return;
    }
    
    return [
        'success' => true,
        'revoked_count' => $revokedCount,
        'message' => $revokedCount > 0 ? "Revoked $revokedCount active token(s)" : 'No active tokens to revoke'
    ];
}

/**
 * List active tokens for a user
 */
function listActiveTokens($conn, $userId) {
    $query = "SELECT id, token, expires_at, created_at, ip_address FROM secure_login_tokens WHERE user_id = ? AND is_used = FALSE AND expires_at > NOW() ORDER BY created_at DESC";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $tokens = [];
    while ($row = $result->fetch_assoc()) {
        $tokens[] = [
            'id' => $row['id'],
            'token' => substr($row['token'], 0, 16) . '...', // Only show first 16 chars for security
            'expires_at' => $row['expires_at'],
            'created_at' => $row['created_at'],
            'ip_address' => $row['ip_address']
        ];
    }
    
    return [
        'success' => true,
        'tokens' => $tokens,
        'count' => count($tokens)
    ];
}

/**
 * Log admin action
 */
function logAdminAction($conn, $adminId, $actionType, $targetUserId, $targetUsername, $deviceId, $details) {
    // Get admin info
    $adminQuery = "SELECT username FROM admin_users WHERE id = ?";
    $stmt = $conn->prepare($adminQuery);
    $stmt->bind_param("i", $adminId);
    $stmt->execute();
    $result = $stmt->get_result();
    $admin = $result->fetch_assoc();
    
    $actionDetails = json_encode($details);
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    $logQuery = "INSERT INTO admin_action_logs (admin_user_id, admin_username, action_type, target_user_id, target_username, target_device_id, action_details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($logQuery);
    $stmt->bind_param("ississsss", $adminId, $admin['username'], $actionType, $targetUserId, $targetUsername, $deviceId, $actionDetails, $ipAddress, $userAgent);
    $stmt->execute();
}
?>
