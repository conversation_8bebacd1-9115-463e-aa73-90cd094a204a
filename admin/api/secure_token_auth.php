<?php
require_once 'config.php';
require_once '../includes/database.php';
require_once '../includes/utilities.php';
require_once '../includes/jwt.php';

// Set JSON content type
header('Content-Type: application/json');

// Allow CORS for Flutter app
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

// Get request data
$data = getRequestData();

// Validate required fields
if (empty($data['secure_token'])) {
    returnError('Secure token is required');
}

$secureToken = sanitizeInput($data['secure_token']);
$deviceId = isset($data['device_id']) ? sanitizeInput($data['device_id']) : null;

// Connect to database
$db = new Database();
$conn = $db->getConnection();

try {
    // Validate secure token
    $tokenResult = validateSecureToken($conn, $secureToken);
    
    if (!$tokenResult['success']) {
        returnError($tokenResult['error'], 401);
    }
    
    $user = $tokenResult['user'];
    
    // Mark token as used
    markTokenAsUsed($conn, $tokenResult['token_id']);
    
    // Update user's device ID if provided
    if ($deviceId) {
        updateUserDeviceId($conn, $user['id'], $deviceId);
        $user['device_id'] = $deviceId;
    }
    
    // Generate never-expiring JWT token for persistent authentication
    $jwtPayload = [
        'user_id' => $user['id'],
        'name' => $user['name'],
        'username' => $user['username'],
        'phone_number' => $user['phone_number'],
        'iat' => time(),
        // No 'exp' field = never expires
        'extended' => true,
        'secure_login' => true,
        'device_id' => $user['device_id']
    ];
    
    $jwtToken = generate_jwt($jwtPayload, APP_SECRET);
    
    // Generate refresh token for token refresh capability
    $refreshTokenPayload = [
        'user_id' => $user['id'],
        'type' => 'refresh',
        'iat' => time(),
        'exp' => time() + (365 * 24 * 60 * 60) // 1 year for refresh token
    ];
    $refreshToken = generate_jwt($refreshTokenPayload, APP_SECRET . '_refresh');
    
    // Log the secure token authentication
    logSecureTokenAuth($conn, $user['id'], $tokenResult['token_id']);
    
    // Update user's last login
    updateUserLastLogin($conn, $user['id']);
    
    // Return success response with tokens
    returnResponse([
        'success' => true,
        'token' => $jwtToken,
        'refresh_token' => $refreshToken,
        'expires_at' => null, // Never expires
        'extended_session' => true,
        'secure_login' => true,
        'user' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'username' => $user['username'],
            'email' => $user['email'],
            'phone_number' => $user['phone_number'],
            'device_id' => $user['device_id']
        ],
        'session_info' => [
            'device_id' => $user['device_id'],
            'login_time' => date('Y-m-d H:i:s'),
            'session_type' => 'secure_token_login',
            'never_expires' => true
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Secure token authentication error: " . $e->getMessage());
    returnError('Internal server error during authentication', 500);
}

/**
 * Validate secure token and return user data
 */
function validateSecureToken($conn, $token) {
    // Validate token format
    if (strlen($token) !== 128) {
        return ['success' => false, 'error' => 'Invalid secure token format'];
    }
    
    // Check if token exists and is valid
    $query = "SELECT slt.id as token_id, slt.*, u.id as user_id, u.username, u.name, u.email, u.phone_number, u.device_id, u.is_active
              FROM secure_login_tokens slt
              JOIN users u ON slt.user_id = u.id
              WHERE slt.token = ? AND slt.is_used = FALSE AND slt.expires_at > NOW()";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return ['success' => false, 'error' => 'Secure token is invalid, expired, or already used'];
    }
    
    $tokenData = $result->fetch_assoc();
    
    // Check if user is active
    if (!$tokenData['is_active']) {
        return ['success' => false, 'error' => 'User account is inactive'];
    }
    
    return [
        'success' => true,
        'token_id' => $tokenData['token_id'],
        'user' => [
            'id' => $tokenData['user_id'],
            'username' => $tokenData['username'],
            'name' => $tokenData['name'],
            'email' => $tokenData['email'],
            'phone_number' => $tokenData['phone_number'],
            'device_id' => $tokenData['device_id'],
            'is_active' => $tokenData['is_active']
        ]
    ];
}

/**
 * Mark secure token as used
 */
function markTokenAsUsed($conn, $tokenId) {
    $updateQuery = "UPDATE secure_login_tokens SET is_used = TRUE, used_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("i", $tokenId);
    $stmt->execute();
}

/**
 * Update user's device ID
 */
function updateUserDeviceId($conn, $userId, $deviceId) {
    $updateQuery = "UPDATE users SET device_id = ?, last_login = NOW() WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("si", $deviceId, $userId);
    $stmt->execute();
}

/**
 * Log secure token authentication
 */
function logSecureTokenAuth($conn, $userId, $tokenId) {
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    $logQuery = "INSERT INTO admin_action_logs (admin_id, action, details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($logQuery);
    
    $action = 'secure_token_used';
    $details = json_encode([
        'user_id' => $userId,
        'token_id' => $tokenId,
        'login_method' => 'flutter_app',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    $stmt->bind_param("issss", $userId, $action, $details, $ipAddress, $userAgent);
    $stmt->execute();
}

/**
 * Update user's last login timestamp
 */
function updateUserLastLogin($conn, $userId) {
    $updateQuery = "UPDATE users SET last_login = NOW() WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
}
?>
