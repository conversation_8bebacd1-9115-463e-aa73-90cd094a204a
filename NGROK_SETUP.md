# 🌐 Ngrok Setup for KFT Fitness App

This guide helps you set up ngrok to expose your local backend server for external access, enabling you to test the Flutter app on real devices or from different networks.

## 📋 Prerequisites

1. **Backend Server Running**: Make sure your PHP backend is running on port 9001
   ```bash
   php -S localhost:9001 -t admin/
   ```

2. **Ngrok Installed**: Install ngrok from https://ngrok.com/download
   - **macOS**: `brew install ngrok`
   - **Linux**: `snap install ngrok`
   - **Windows**: Download from https://ngrok.com/download

## 🚀 Quick Start

### Method 1: Using the Automated Script

1. **Run the setup script**:
   ```bash
   ./start_ngrok.sh
   ```

2. **Copy the ngrok URL** from the output (e.g., `https://abc123.ngrok-free.app`)

3. **Configure Flutter app**:
   - Open the Flutter app
   - Go to Dev Tools (if available) or use the ngrok configuration dialog
   - Paste the ngrok URL
   - Test the connection

### Method 2: Manual Setup

1. **Start ngrok**:
   ```bash
   ngrok http 9001
   ```

2. **Copy the HTTPS URL** from the ngrok output:
   ```
   Forwarding    https://abc123.ngrok-free.app -> http://localhost:9001
   ```

3. **Update Flutter configuration**:
   - Open `lib/config/network_config.dart`
   - Update the `ngrokEndpoint` constant:
   ```dart
   static const String ngrokEndpoint = 'https://abc123.ngrok-free.app/admin/api/';
   ```

4. **Restart the Flutter app** to use the new configuration

## 📱 Flutter App Configuration

### Using Dev Tools (Recommended)

1. Open the Flutter app
2. Navigate to Dev Tools page
3. Click "Configure Ngrok"
4. Paste your ngrok URL (e.g., `https://abc123.ngrok-free.app`)
5. Click "Update"
6. Test the connection

### Manual Configuration

Update the ngrok endpoint in `lib/config/network_config.dart`:

```dart
// Update this line with your actual ngrok URL
static const String ngrokEndpoint = 'https://your-ngrok-url.ngrok-free.app/admin/api/';
```

## 🔧 Configuration Details

### Network Configuration Priority

The app will try endpoints in this order:
1. **Ngrok URL** (if configured)
2. `http://localhost:9001/admin/api/`
3. `http://127.0.0.1:9001/admin/api/`
4. `http://***************:9001/admin/api/`

### Backend Compatibility

The backend has been updated to support both:
- **Username-based login**: `{"username": "john_doe", "pin": "1234"}`
- **Phone-based login**: `{"phone_number": "+919876543210", "pin": "1234"}`

## 🧪 Testing

### Test Connection
1. Use the Dev Tools page in the Flutter app
2. Click "Test Connection"
3. Check for successful response

### Test Login
1. Try logging in with a mobile number
2. Select country code (+91 is default)
3. Enter phone number and PIN

## 🔍 Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure backend server is running on port 9001
   - Check if ngrok tunnel is active

2. **Invalid ngrok URL**
   - Make sure URL starts with `https://`
   - Ensure URL contains `.ngrok-free.app` or `.ngrok.io`
   - Don't include `/admin/api/` in the base URL (it's added automatically)

3. **Phone Login Not Working**
   - Ensure user exists in database with phone number
   - Check phone number format (include country code)
   - Verify PIN is correct

### Debug Information

Use the Dev Tools page to:
- View current endpoint
- Test connection
- Switch between endpoints
- Copy endpoint URLs

## 📝 Notes

- **Free ngrok**: URLs change each time you restart ngrok
- **Paid ngrok**: You can get persistent URLs
- **Security**: ngrok URLs are publicly accessible
- **Performance**: There might be slight latency through ngrok tunnel

## 🔗 Useful Links

- [Ngrok Documentation](https://ngrok.com/docs)
- [Ngrok Dashboard](https://dashboard.ngrok.com/)
- [Flutter Network Configuration](lib/config/network_config.dart)

## 📞 Support

If you encounter issues:
1. Check the console output for error messages
2. Use the Dev Tools page to diagnose connection issues
3. Verify backend server is accessible locally first
4. Test ngrok tunnel with a browser before using in Flutter app
