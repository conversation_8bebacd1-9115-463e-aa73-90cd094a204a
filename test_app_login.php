<?php
// Test the app-login.php functionality
require_once 'admin/includes/config.php';
require_once 'admin/includes/database.php';

echo "<h1>🚀 App Login Test</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Find a test user
    $userResult = $conn->query("SELECT id, username, name FROM users WHERE is_active = 1 LIMIT 1");
    if ($userResult->num_rows > 0) {
        $testUser = $userResult->fetch_assoc();
        
        // Generate a test token
        $token = bin2hex(random_bytes(64));
        $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60));
        
        $stmt = $conn->prepare("INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("isis", $testUser['id'], $token, $testUser['id'], $expiresAt);
        
        if ($stmt->execute()) {
            $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
            $testUrl = $baseUrl . "/admin/app-login.php?secure_token=" . urlencode($token);
            
            echo "<h2>✅ Test Token Generated</h2>";
            echo "<p><strong>User:</strong> {$testUser['name']} (ID: {$testUser['id']})</p>";
            echo "<p><strong>Token:</strong> " . substr($token, 0, 20) . "...</p>";
            echo "<p><strong>Expires:</strong> $expiresAt</p>";
            
            echo "<h2>🔗 Test the App Login Flow</h2>";
            echo "<p>Click the link below to test the app login functionality:</p>";
            echo "<p><a href='$testUrl' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Test App Login</a></p>";
            echo "<p><small>URL: " . htmlspecialchars($testUrl) . "</small></p>";
            
            echo "<h2>📱 What Should Happen</h2>";
            echo "<ol>";
            echo "<li>On mobile: Should try to open the KFT Fitness app</li>";
            echo "<li>If app not installed: Should show fallback options</li>";
            echo "<li>On desktop: Should redirect to web login</li>";
            echo "<li>The secure token should authenticate the user automatically</li>";
            echo "</ol>";
            
        } else {
            echo "<p>❌ Failed to create test token</p>";
        }
    } else {
        echo "<p>❌ No active users found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
