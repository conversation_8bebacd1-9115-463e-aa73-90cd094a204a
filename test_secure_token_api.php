<?php
// Comprehensive test for secure token API
require_once 'admin/includes/config.php';
require_once 'admin/includes/database.php';
require_once 'admin/includes/utilities.php';

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>🔐 Secure Token API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        .btn { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .url-box { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Secure Token API Comprehensive Test</h1>
        
        <?php
        try {
            echo "<div class='test-section info'>";
            echo "<h2>📋 Test Configuration</h2>";
            echo "<p><strong>API Base URL:</strong> " . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . "/admin/api/</p>";
            echo "<p><strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
            echo "</div>";

            // Test 1: Database Connection
            echo "<div class='test-section'>";
            echo "<h2>🗄️ Test 1: Database Connection</h2>";
            
            $db = new Database();
            $conn = $db->getConnection();
            echo "<p class='success'>✅ Database connection successful</p>";
            
            // Check tables
            $tables = ['users', 'secure_login_tokens', 'admin_action_logs'];
            foreach ($tables as $table) {
                $result = $conn->query("SHOW TABLES LIKE '$table'");
                if ($result->num_rows > 0) {
                    echo "<p class='success'>✅ Table '$table' exists</p>";
                } else {
                    echo "<p class='error'>❌ Table '$table' missing</p>";
                }
            }
            echo "</div>";

            // Test 2: Create Test User and Token
            echo "<div class='test-section'>";
            echo "<h2>👤 Test 2: Create Test Token</h2>";
            
            $userResult = $conn->query("SELECT id, username, name, phone_number FROM users WHERE is_active = 1 LIMIT 1");
            if ($userResult->num_rows > 0) {
                $testUser = $userResult->fetch_assoc();
                echo "<p class='info'>📱 Using test user: <strong>{$testUser['name']}</strong> (ID: {$testUser['id']}, Phone: {$testUser['phone_number']})</p>";
                
                // Generate test token
                $token = bin2hex(random_bytes(64));
                $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60));
                
                $stmt = $conn->prepare("INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at) VALUES (?, ?, ?, ?)");
                $stmt->bind_param("isis", $testUser['id'], $token, $testUser['id'], $expiresAt);
                
                if ($stmt->execute()) {
                    echo "<p class='success'>✅ Test token created successfully</p>";
                    echo "<div class='code'>Token: " . substr($token, 0, 30) . "...\nExpires: $expiresAt</div>";
                    
                    // Test 3: API Authentication
                    echo "</div><div class='test-section'>";
                    echo "<h2>🔌 Test 3: API Authentication Test</h2>";
                    
                    $apiUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . '/admin/api/secure_token_auth.php';
                    $testData = json_encode([
                        'secure_token' => $token,
                        'device_id' => 'test_device_' . time()
                    ]);
                    
                    echo "<p class='info'>🌐 Testing API endpoint: <code>$apiUrl</code></p>";
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $apiUrl);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Content-Type: application/json',
                        'Content-Length: ' . strlen($testData)
                    ]);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                    
                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    $curlError = curl_error($ch);
                    curl_close($ch);
                    
                    echo "<p class='info'>📡 HTTP Response Code: <strong>$httpCode</strong></p>";
                    
                    if ($curlError) {
                        echo "<p class='error'>❌ cURL Error: $curlError</p>";
                    } elseif ($response) {
                        $responseData = json_decode($response, true);
                        if ($responseData) {
                            echo "<div class='code'>" . json_encode($responseData, JSON_PRETTY_PRINT) . "</div>";
                            
                            if (isset($responseData['success']) && $responseData['success'] === true) {
                                echo "<p class='success'>✅ API authentication successful!</p>";
                                echo "<p class='success'>🎉 JWT Token generated: " . substr($responseData['token'], 0, 50) . "...</p>";
                                echo "<p class='success'>🔄 Refresh Token: " . (isset($responseData['refresh_token']) ? 'Yes' : 'No') . "</p>";
                                echo "<p class='success'>⏰ Token Expiry: " . ($responseData['expires_at'] ?? 'Never expires') . "</p>";
                                
                                // Test 4: Generate URLs
                                echo "</div><div class='test-section'>";
                                echo "<h2>🔗 Test 4: Generated URLs</h2>";
                                
                                $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
                                $appDeepLink = "kftfitness://login?secure_token=" . urlencode($token);
                                $universalLink = $baseUrl . "/admin/app-login.php?secure_token=" . urlencode($token);
                                $webLink = $baseUrl . "/admin/login.php?secure_token=" . urlencode($token);
                                
                                echo "<div class='url-box'>";
                                echo "<h4>📱 Flutter App Deep Link:</h4>";
                                echo "<code>$appDeepLink</code>";
                                echo "<button class='btn' onclick='copyToClipboard(\"$appDeepLink\")'>Copy</button>";
                                echo "</div>";
                                
                                echo "<div class='url-box'>";
                                echo "<h4>🌐 Universal Link (Mobile):</h4>";
                                echo "<code>$universalLink</code>";
                                echo "<button class='btn' onclick='copyToClipboard(\"$universalLink\")'>Copy</button>";
                                echo "<a href='$universalLink' target='_blank' class='btn'>Test</a>";
                                echo "</div>";
                                
                                echo "<div class='url-box'>";
                                echo "<h4>💻 Web Admin Link:</h4>";
                                echo "<code>$webLink</code>";
                                echo "<button class='btn' onclick='copyToClipboard(\"$webLink\")'>Copy</button>";
                                echo "<a href='$webLink' target='_blank' class='btn'>Test</a>";
                                echo "</div>";
                                
                            } else {
                                echo "<p class='error'>❌ API returned error: " . ($responseData['error'] ?? 'Unknown error') . "</p>";
                            }
                        } else {
                            echo "<p class='error'>❌ Invalid JSON response</p>";
                            echo "<div class='code'>Raw response: " . htmlspecialchars($response) . "</div>";
                        }
                    } else {
                        echo "<p class='error'>❌ No response from API</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Failed to create test token</p>";
                }
            } else {
                echo "<p class='error'>❌ No active users found for testing</p>";
            }
            echo "</div>";

            // Test 5: Instructions
            echo "<div class='test-section info'>";
            echo "<h2>📱 Test 5: Flutter App Testing Instructions</h2>";
            echo "<ol>";
            echo "<li><strong>For Mobile Testing:</strong> Copy the Universal Link and open it on your mobile device</li>";
            echo "<li><strong>For Deep Link Testing:</strong> Copy the App Deep Link and test it with a deep link tester app</li>";
            echo "<li><strong>Expected Behavior:</strong> The app should open and automatically log in the user</li>";
            echo "<li><strong>Persistent Login:</strong> User should remain logged in permanently (no auto-logout)</li>";
            echo "<li><strong>Profile Access:</strong> User should be able to access their profile and all app features</li>";
            echo "</ol>";
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='test-section error'>";
            echo "<h2>❌ Test Failed</h2>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        ?>
        
        <div class="test-section">
            <h2>🔄 Actions</h2>
            <button class="btn" onclick="location.reload()">Refresh Test</button>
            <button class="btn" onclick="window.open('/admin/user_view.php?id=1', '_blank')">Open User Management</button>
            <button class="btn" onclick="window.open('/test_secure_token.php', '_blank')">Run Basic Test</button>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text).then(function() {
                    alert('Copied to clipboard!');
                }).catch(function() {
                    fallbackCopy(text);
                });
            } else {
                fallbackCopy(text);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                alert('Copied to clipboard!');
            } catch (err) {
                alert('Failed to copy. Please copy manually: ' + text);
            }
            document.body.removeChild(textArea);
        }
    </script>
</body>
</html>
