<?php
// Simple test script for secure token functionality
require_once 'admin/includes/config.php';
require_once 'admin/includes/database.php';
require_once 'admin/includes/utilities.php';

echo "<h1>🔐 Secure Token System Test</h1>";

// Test 1: Check if secure_token_auth.php exists
echo "<h2>Test 1: API Endpoint Check</h2>";
if (file_exists('admin/api/secure_token_auth.php')) {
    echo "✅ secure_token_auth.php exists<br>";
} else {
    echo "❌ secure_token_auth.php missing<br>";
}

// Test 2: Check database connection
echo "<h2>Test 2: Database Connection</h2>";
try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connection successful<br>";
    
    // Check if secure_login_tokens table exists
    $result = $conn->query("SHOW TABLES LIKE 'secure_login_tokens'");
    if ($result->num_rows > 0) {
        echo "✅ secure_login_tokens table exists<br>";
    } else {
        echo "❌ secure_login_tokens table missing<br>";
    }
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test 3: Create a test secure token
echo "<h2>Test 3: Test Token Creation</h2>";
try {
    // Find a test user
    $userResult = $conn->query("SELECT id, username, name FROM users WHERE is_active = 1 LIMIT 1");
    if ($userResult->num_rows > 0) {
        $testUser = $userResult->fetch_assoc();
        echo "Using test user: {$testUser['name']} (ID: {$testUser['id']})<br>";
        
        // Generate a test token
        $token = bin2hex(random_bytes(64));
        $expiresAt = date('Y-m-d H:i:s', time() + (24 * 60 * 60));
        
        $stmt = $conn->prepare("INSERT INTO secure_login_tokens (user_id, token, generated_by_admin, expires_at) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("isis", $testUser['id'], $token, $testUser['id'], $expiresAt);
        
        if ($stmt->execute()) {
            echo "✅ Test token generated successfully<br>";
            echo "Token: " . substr($token, 0, 20) . "...<br>";
            echo "Expires: " . $expiresAt . "<br>";
            
            // Test the secure token authentication API
            echo "<h2>Test 4: API Authentication Test</h2>";
            
            $testData = json_encode([
                'secure_token' => $token,
                'device_id' => 'test_device_' . time()
            ]);
            
            // Create a test URL for the Flutter app
            $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
            $appDeepLink = "kftfitness://login?secure_token=" . urlencode($token);
            $universalLink = $baseUrl . "/admin/app-login.php?secure_token=" . urlencode($token);
            
            echo "<strong>Generated URLs:</strong><br>";
            echo "App Deep Link: <code>" . htmlspecialchars($appDeepLink) . "</code><br>";
            echo "Universal Link: <a href='" . htmlspecialchars($universalLink) . "' target='_blank'>" . htmlspecialchars($universalLink) . "</a><br>";
            
            // Test API call using cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl . '/admin/api/secure_token_auth.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($testData)
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "<strong>API Response (HTTP $httpCode):</strong><br>";
            if ($response) {
                $responseData = json_decode($response, true);
                if ($responseData) {
                    echo "<pre>" . json_encode($responseData, JSON_PRETTY_PRINT) . "</pre>";
                    
                    if (isset($responseData['success']) && $responseData['success'] === true) {
                        echo "✅ Secure token authentication API working correctly!<br>";
                        echo "🎉 <strong>The secure token system is ready to use!</strong><br>";
                    } else {
                        echo "❌ API returned error: " . ($responseData['error'] ?? 'Unknown error') . "<br>";
                    }
                } else {
                    echo "❌ Invalid JSON response: " . htmlspecialchars($response) . "<br>";
                }
            } else {
                echo "❌ No response from API<br>";
            }
            
        } else {
            echo "❌ Failed to create test token<br>";
        }
    } else {
        echo "❌ No active users found for testing<br>";
    }
} catch (Exception $e) {
    echo "❌ Error in test: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>📱 How to Test with Flutter App</h2>";
echo "<ol>";
echo "<li>Generate a secure login link from the admin panel (user_view.php)</li>";
echo "<li>Copy the generated link</li>";
echo "<li>Open the link on a mobile device or in a browser</li>";
echo "<li>The link should redirect to the Flutter app with automatic login</li>";
echo "<li>User should be logged in permanently (no expiry, no logout)</li>";
echo "</ol>";

echo "<h2>🔗 Test Links</h2>";
echo "<p>If you have a test token, you can test these URLs:</p>";
echo "<ul>";
echo "<li><strong>Deep Link:</strong> kftfitness://login?secure_token=YOUR_TOKEN_HERE</li>";
echo "<li><strong>Universal Link:</strong> " . $baseUrl . "/admin/app-login.php?secure_token=YOUR_TOKEN_HERE</li>";
echo "</ul>";
?>
