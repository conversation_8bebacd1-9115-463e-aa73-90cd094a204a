import 'dart:convert';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import '../services/persistent_auth_service.dart';
import '../providers/auth_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Service to handle secure token authentication from URL parameters
class SecureTokenAuthService {
  static final SecureTokenAuthService _instance = SecureTokenAuthService._internal();
  factory SecureTokenAuthService() => _instance;
  SecureTokenAuthService._internal();

  final PersistentAuthService _persistentAuthService = PersistentAuthService();
  bool _isProcessingToken = false;

  /// Check for secure token in URL and authenticate if found
  Future<bool> checkAndProcessSecureToken(WidgetRef ref) async {
    if (!kIsWeb || _isProcessingToken) return false;

    try {
      _isProcessingToken = true;
      debugPrint('🔐 Checking for secure token in URL...');

      // Get current URL
      final currentUrl = html.window.location.href;
      final uri = Uri.parse(currentUrl);
      
      // Check for secure_token parameter
      final secureToken = uri.queryParameters['secure_token'];
      
      if (secureToken == null || secureToken.isEmpty) {
        debugPrint('🔐 No secure token found in URL');
        return false;
      }

      debugPrint('🔐 Secure token found in URL: ${secureToken.substring(0, 20)}...');

      // Validate token format
      if (secureToken.length != 128) {
        debugPrint('❌ Invalid secure token format');
        _cleanUrlParameters();
        return false;
      }

      // Authenticate with secure token
      final authResult = await _authenticateWithSecureToken(secureToken);
      
      if (authResult['success'] == true) {
        debugPrint('✅ Secure token authentication successful');
        
        // Save the JWT token
        final jwtToken = authResult['token'];
        if (jwtToken != null) {
          await _persistentAuthService.saveToken(jwtToken, authResult['refresh_token']);
          
          // Update auth provider
          await ref.read(authProvider.notifier).login(jwtToken);
          
          debugPrint('✅ User authenticated via secure token');
        }
        
        // Clean URL parameters to remove the token
        _cleanUrlParameters();
        
        return true;
      } else {
        debugPrint('❌ Secure token authentication failed: ${authResult['error']}');
        _cleanUrlParameters();
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error processing secure token: $e');
      _cleanUrlParameters();
      return false;
    } finally {
      _isProcessingToken = false;
    }
  }

  /// Authenticate with the secure token via API
  Future<Map<String, dynamic>> _authenticateWithSecureToken(String secureToken) async {
    try {
      final url = Uri.parse('${AppConfig.defaultApiBaseUrl}/secure_token_auth.php');
      
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'secure_token': secureToken,
        }),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        debugPrint('🔐 Secure token API response: ${data['success']}');
        return data;
      } else {
        debugPrint('❌ Secure token API error: ${response.statusCode}');
        return {
          'success': false,
          'error': 'Authentication server error (${response.statusCode})'
        };
      }
    } catch (e) {
      debugPrint('❌ Secure token authentication error: $e');
      return {
        'success': false,
        'error': 'Network error during authentication'
      };
    }
  }

  /// Clean URL parameters to remove the secure token
  void _cleanUrlParameters() {
    if (!kIsWeb) return;

    try {
      final currentUrl = html.window.location.href;
      final uri = Uri.parse(currentUrl);
      
      // Create new URL without secure_token parameter
      final cleanParams = Map<String, String>.from(uri.queryParameters);
      cleanParams.remove('secure_token');
      
      // Build clean URL
      final cleanUri = uri.replace(queryParameters: cleanParams.isEmpty ? null : cleanParams);
      
      // Update browser URL without page reload
      html.window.history.replaceState(null, '', cleanUri.toString());
      
      debugPrint('🧹 Cleaned secure token from URL');
    } catch (e) {
      debugPrint('⚠️ Failed to clean URL parameters: $e');
    }
  }

  /// Check if there's a secure token in the current URL
  bool hasSecureTokenInUrl() {
    if (!kIsWeb) return false;

    try {
      final currentUrl = html.window.location.href;
      final uri = Uri.parse(currentUrl);
      final secureToken = uri.queryParameters['secure_token'];
      
      return secureToken != null && secureToken.isNotEmpty && secureToken.length == 128;
    } catch (e) {
      debugPrint('⚠️ Error checking for secure token in URL: $e');
      return false;
    }
  }

  /// Get secure token from URL if present
  String? getSecureTokenFromUrl() {
    if (!kIsWeb) return null;

    try {
      final currentUrl = html.window.location.href;
      final uri = Uri.parse(currentUrl);
      final secureToken = uri.queryParameters['secure_token'];
      
      if (secureToken != null && secureToken.length == 128) {
        return secureToken;
      }
      
      return null;
    } catch (e) {
      debugPrint('⚠️ Error getting secure token from URL: $e');
      return null;
    }
  }

  /// Reset processing state (for testing)
  void resetProcessingState() {
    _isProcessingToken = false;
  }
}
