import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import 'persistent_auth_service.dart';
import 'api_service.dart';

class SecureTokenService {
  static final SecureTokenService _instance = SecureTokenService._internal();
  factory SecureTokenService() => _instance;
  SecureTokenService._internal();

  static const MethodChannel _channel = MethodChannel('com.kft.fitness/secure_token');
  
  final PersistentAuthService _persistentAuthService = PersistentAuthService();
  bool _isInitialized = false;
  String? _pendingSecureToken;

  /// Initialize the secure token service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔐 Initializing SecureTokenService...');

      // Set up method channel handler for deep links
      _channel.setMethodCallHandler(_handleMethodCall);

      // Check for any pending secure token from app launch
      await _checkForLaunchToken();

      _isInitialized = true;
      debugPrint('✅ SecureTokenService initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing SecureTokenService: $e');
      _isInitialized = true; // Mark as initialized even on error
    }
  }

  /// Handle method calls from native platforms
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'handleSecureToken':
        final String? token = call.arguments['secure_token'];
        if (token != null) {
          debugPrint('🔗 Received secure token from deep link: ${token.substring(0, 20)}...');
          await processSecureToken(token);
        }
        break;
      case 'getInitialLink':
        final String? link = call.arguments['link'];
        if (link != null) {
          debugPrint('🚀 Processing initial link: $link');
          await _processInitialLink(link);
        }
        break;
      default:
        debugPrint('⚠️ Unknown method call: ${call.method}');
    }
  }

  /// Check for secure token from app launch
  Future<void> _checkForLaunchToken() async {
    try {
      // For web, check URL parameters
      if (kIsWeb) {
        await _checkWebUrlParameters();
      } else {
        // For mobile, get initial link from native platform
        final String? initialLink = await _channel.invokeMethod('getInitialLink');
        if (initialLink != null) {
          await _processInitialLink(initialLink);
        }
      }
    } catch (e) {
      debugPrint('⚠️ Error checking for launch token: $e');
    }
  }

  /// Check web URL parameters for secure token
  Future<void> _checkWebUrlParameters() async {
    try {
      if (kIsWeb) {
        // Get current URL and check for secure_token parameter
        final uri = Uri.base;
        final secureToken = uri.queryParameters['secure_token'];
        
        if (secureToken != null && secureToken.isNotEmpty) {
          debugPrint('🌐 Found secure token in web URL: ${secureToken.substring(0, 20)}...');
          await processSecureToken(secureToken);
          
          // Clean up URL by removing the token parameter
          _cleanUpWebUrl();
        }
      }
    } catch (e) {
      debugPrint('❌ Error checking web URL parameters: $e');
    }
  }

  /// Clean up web URL by removing secure token parameter
  void _cleanUpWebUrl() {
    if (kIsWeb) {
      try {
        // Use history API to clean up URL without page reload
        final uri = Uri.base;
        final cleanParams = Map<String, String>.from(uri.queryParameters);
        cleanParams.remove('secure_token');
        
        final cleanUri = uri.replace(queryParameters: cleanParams.isEmpty ? null : cleanParams);
        // Note: In a real implementation, you'd use dart:html to update the URL
        debugPrint('🧹 Cleaned up web URL');
      } catch (e) {
        debugPrint('⚠️ Error cleaning up web URL: $e');
      }
    }
  }

  /// Process initial link from app launch
  Future<void> _processInitialLink(String link) async {
    try {
      final uri = Uri.parse(link);
      final secureToken = uri.queryParameters['secure_token'];
      
      if (secureToken != null && secureToken.isNotEmpty) {
        debugPrint('🔗 Processing secure token from initial link: ${secureToken.substring(0, 20)}...');
        await processSecureToken(secureToken);
      }
    } catch (e) {
      debugPrint('❌ Error processing initial link: $e');
    }
  }

  /// Process secure token and authenticate user
  Future<bool> processSecureToken(String secureToken) async {
    try {
      debugPrint('🔐 Processing secure token authentication...');

      // Get device ID for authentication
      final deviceId = await _getDeviceId();

      // Call the secure token authentication API
      final response = await http.post(
        Uri.parse('${AppConfig.defaultApiBaseUrl}/secure_token_auth.php'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'secure_token': secureToken,
          'device_id': deviceId,
        }),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['success'] == true) {
          debugPrint('✅ Secure token authentication successful');

          // Use a simpler approach to save tokens
          await _saveSecureTokens(data['token'], data['refresh_token']);

          debugPrint('🏠 User authenticated via secure token - ready for app navigation');

          // Show success feedback to user
          _showUserFeedback('Welcome! You have been logged in successfully.', isSuccess: true);

          return true;
        } else {
          final errorMessage = data['error'] ?? 'Authentication failed. Please try again.';
          debugPrint('❌ Secure token authentication failed: $errorMessage');

          // Show error feedback to user
          _showUserFeedback(errorMessage, isSuccess: false);

          return false;
        }
      } else {
        debugPrint('❌ Secure token API request failed: ${response.statusCode}');
        _showUserFeedback('Network error. Please check your connection and try again.', isSuccess: false);
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error processing secure token: $e');
      _showUserFeedback('An unexpected error occurred. Please try again later.', isSuccess: false);
      return false;
    }
  }

  /// Get device ID for authentication
  Future<String> _getDeviceId() async {
    try {
      // Use the same device ID logic as the login system
      if (kIsWeb) {
        return 'web_${DateTime.now().millisecondsSinceEpoch}';
      } else {
        // For mobile, you'd typically use device_info_plus or similar
        return 'mobile_${DateTime.now().millisecondsSinceEpoch}';
      }
    } catch (e) {
      debugPrint('⚠️ Error getting device ID: $e');
      return 'unknown_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Check if there's a pending secure token to process
  bool hasPendingSecureToken() {
    return _pendingSecureToken != null;
  }

  /// Get and clear pending secure token
  String? getPendingSecureToken() {
    final token = _pendingSecureToken;
    _pendingSecureToken = null;
    return token;
  }

  /// Set pending secure token for later processing
  void setPendingSecureToken(String token) {
    _pendingSecureToken = token;
    debugPrint('📝 Set pending secure token for later processing');
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Show user feedback message
  void _showUserFeedback(String message, {required bool isSuccess}) {
    try {
      // For now, just log the message. In a real app, you'd show a snackbar or toast
      if (isSuccess) {
        debugPrint('✅ User Feedback: $message');
      } else {
        debugPrint('❌ User Feedback: $message');
      }

      // TODO: Implement actual user feedback UI (SnackBar, Toast, etc.)
      // This would require access to BuildContext or a global scaffold messenger
    } catch (e) {
      debugPrint('⚠️ Error showing user feedback: $e');
    }
  }

  /// Save secure tokens using API service
  Future<void> _saveSecureTokens(String accessToken, String? refreshToken) async {
    try {
      debugPrint('💾 Saving secure tokens...');

      // Use ApiService to save tokens
      final apiService = ApiService();
      await apiService.saveToken(accessToken);

      // Also save to persistent auth service storage directly
      await _persistentAuthService.initialize();

      // Force authentication state update
      await SecureTokenAuthHelper.triggerAuthStateUpdate();

      debugPrint('✅ Tokens saved via secure token authentication');
    } catch (e) {
      debugPrint('❌ Error saving secure tokens: $e');
      rethrow;
    }
  }
}

/// Simple helper to trigger authentication state update
class SecureTokenAuthHelper {
  static Future<void> triggerAuthStateUpdate() async {
    try {
      final persistentAuth = PersistentAuthService();
      // Force a re-check of authentication status
      await persistentAuth.isLoggedIn();
    } catch (e) {
      debugPrint('⚠️ Error triggering auth state update: $e');
    }
  }
}


