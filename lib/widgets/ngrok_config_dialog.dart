import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/network_config.dart';
import '../design_system/kft_design_system.dart';

/// Ngrok Configuration Dialog
/// Allows developers to easily update the ngrok URL during development
class NgrokConfigDialog extends StatefulWidget {
  const NgrokConfigDialog({Key? key}) : super(key: key);

  static void show(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const NgrokConfigDialog(),
    );
  }

  @override
  State<NgrokConfigDialog> createState() => _NgrokConfigDialogState();
}

class _NgrokConfigDialogState extends State<NgrokConfigDialog> {
  final _urlController = TextEditingController();
  bool _isValidUrl = false;

  @override
  void initState() {
    super.initState();
    _urlController.text = NetworkConfig.ngrokEndpoint;
    _validateUrl(_urlController.text);
  }

  void _validateUrl(String url) {
    setState(() {
      _isValidUrl = url.isNotEmpty && 
                   (url.startsWith('https://') || url.startsWith('http://')) &&
                   url.contains('ngrok');
    });
  }

  void _updateNgrokUrl() {
    if (_isValidUrl) {
      NetworkConfig.updateNgrokUrl(_urlController.text);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Ngrok URL updated successfully!'),
          backgroundColor: KFTDesignSystem.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
      
      Navigator.of(context).pop();
    }
  }

  void _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        _urlController.text = clipboardData!.text!;
        _validateUrl(clipboardData.text!);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to paste from clipboard: $e'),
          backgroundColor: KFTDesignSystem.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.settings_ethernet,
            color: KFTDesignSystem.primaryColor,
          ),
          const SizedBox(width: 8),
          const Text('Ngrok Configuration'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enter your ngrok URL to connect to the backend server:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            
            // URL Input Field
            TextField(
              controller: _urlController,
              decoration: InputDecoration(
                labelText: 'Ngrok URL',
                hintText: 'https://abc123.ngrok-free.app',
                prefixIcon: const Icon(Icons.link),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.paste),
                  onPressed: _pasteFromClipboard,
                  tooltip: 'Paste from clipboard',
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                errorText: _urlController.text.isNotEmpty && !_isValidUrl 
                  ? 'Please enter a valid ngrok URL' 
                  : null,
              ),
              onChanged: _validateUrl,
              keyboardType: TextInputType.url,
            ),
            
            const SizedBox(height: 16),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, size: 16, color: Colors.blue.shade700),
                      const SizedBox(width: 8),
                      Text(
                        'How to get your ngrok URL:',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '1. Run: ngrok http 9001\n'
                    '2. Copy the HTTPS URL (e.g., https://abc123.ngrok-free.app)\n'
                    '3. Paste it above and click Update',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Current Status
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Current Configuration:',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Endpoint: ${NetworkConfig.getCurrentEndpoint()}',
                    style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isValidUrl ? _updateNgrokUrl : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: KFTDesignSystem.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('Update'),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }
}
