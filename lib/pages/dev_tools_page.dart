import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/network_config.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/ngrok_config_dialog.dart';

/// Development Tools Page
/// Provides tools for developers to configure and test the app
class DevToolsPage extends StatefulWidget {
  const DevToolsPage({Key? key}) : super(key: key);

  @override
  State<DevToolsPage> createState() => _DevToolsPageState();
}

class _DevToolsPageState extends State<DevToolsPage> {
  Map<String, dynamic> _networkStatus = {};
  bool _isTestingConnection = false;

  @override
  void initState() {
    super.initState();
    _updateNetworkStatus();
  }

  void _updateNetworkStatus() {
    setState(() {
      _networkStatus = NetworkConfig.getNetworkStatus();
    });
  }

  Future<void> _testConnection() async {
    setState(() {
      _isTestingConnection = true;
    });

    try {
      final currentEndpoint = NetworkConfig.getCurrentEndpoint();
      final isConnected = await NetworkConfig.testEndpointConnectivity(currentEndpoint);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isConnected 
              ? 'Connection successful!' 
              : 'Connection failed. Check your server and network.',
          ),
          backgroundColor: isConnected 
            ? KFTDesignSystem.successColor 
            : KFTDesignSystem.errorColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Connection test failed: $e'),
          backgroundColor: KFTDesignSystem.errorColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      setState(() {
        _isTestingConnection = false;
      });
    }
  }

  void _switchEndpoint() {
    NetworkConfig.switchToNextEndpoint();
    _updateNetworkStatus();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Switched to: ${NetworkConfig.getCurrentEndpoint()}'),
        backgroundColor: KFTDesignSystem.primaryColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _copyEndpointToClipboard() {
    Clipboard.setData(ClipboardData(text: NetworkConfig.getCurrentEndpoint()));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Endpoint copied to clipboard'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Development Tools'),
        backgroundColor: KFTDesignSystem.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _updateNetworkStatus,
            tooltip: 'Refresh Status',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Network Configuration Section
            _buildSectionCard(
              title: 'Network Configuration',
              icon: Icons.network_check,
              children: [
                _buildInfoRow('Current Endpoint', _networkStatus['current_endpoint'] ?? 'Unknown'),
                _buildInfoRow('Debug Mode', _networkStatus['is_debug_mode']?.toString() ?? 'Unknown'),
                _buildInfoRow('Endpoint Index', _networkStatus['current_index']?.toString() ?? 'Unknown'),
                
                const SizedBox(height: 16),
                
                // Action Buttons
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        NgrokConfigDialog.show(context).then((_) {
                          _updateNetworkStatus();
                        });
                      },
                      icon: const Icon(Icons.settings_ethernet),
                      label: const Text('Configure Ngrok'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: KFTDesignSystem.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: _isTestingConnection ? null : _testConnection,
                      icon: _isTestingConnection 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.wifi_tethering),
                      label: Text(_isTestingConnection ? 'Testing...' : 'Test Connection'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: KFTDesignSystem.successColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: _switchEndpoint,
                      icon: const Icon(Icons.swap_horiz),
                      label: const Text('Switch Endpoint'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: KFTDesignSystem.warningColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Available Endpoints Section
            _buildSectionCard(
              title: 'Available Endpoints',
              icon: Icons.list,
              children: [
                if (_networkStatus['available_endpoints'] != null)
                  ..._networkStatus['available_endpoints'].map<Widget>((endpoint) {
                    final isCurrent = endpoint == _networkStatus['current_endpoint'];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isCurrent ? KFTDesignSystem.primaryColor.withOpacity(0.1) : Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isCurrent ? KFTDesignSystem.primaryColor : Colors.grey.shade300,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            isCurrent ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                            color: isCurrent ? KFTDesignSystem.primaryColor : Colors.grey,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              endpoint,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                                fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
                                color: isCurrent ? KFTDesignSystem.primaryColor : Colors.black87,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.copy, size: 16),
                            onPressed: () {
                              Clipboard.setData(ClipboardData(text: endpoint));
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Endpoint copied to clipboard'),
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                            },
                            tooltip: 'Copy to clipboard',
                          ),
                        ],
                      ),
                    );
                  }).toList(),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Instructions Section
            _buildSectionCard(
              title: 'Ngrok Setup Instructions',
              icon: Icons.help_outline,
              children: [
                const Text(
                  'To use ngrok with your backend:',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                const Text(
                  '1. Install ngrok: https://ngrok.com/download\n'
                  '2. Run: ngrok http 9001\n'
                  '3. Copy the HTTPS URL from ngrok output\n'
                  '4. Click "Configure Ngrok" above and paste the URL\n'
                  '5. Test the connection',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: KFTDesignSystem.primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }
}
