import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/magic_login_service.dart';
import '../theme/app_theme.dart';

class MagicLoginPage extends ConsumerStatefulWidget {
  final String token;

  const MagicLoginPage({
    Key? key,
    required this.token,
  }) : super(key: key);

  @override
  ConsumerState<MagicLoginPage> createState() => _MagicLoginPageState();
}

class _MagicLoginPageState extends ConsumerState<MagicLoginPage>
    with SingleTickerProviderStateMixin {
  final MagicLoginService _magicLoginService = MagicLoginService();
  
  bool _isLoading = true;
  String? _errorMessage;
  String? _successMessage;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start animation
    _animationController.forward();
    
    // Process magic login token
    _processMagicLogin();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Process the magic login token
  Future<void> _processMagicLogin() async {
    try {
      debugPrint('🪄 Processing magic login token: ${widget.token.substring(0, 20)}...');
      
      // Validate token format first
      if (!_magicLoginService.isValidMagicToken(widget.token)) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Invalid magic login link format. Please request a new link.';
        });
        return;
      }

      // Add a small delay for better UX
      await Future.delayed(const Duration(milliseconds: 1500));

      // Authenticate with magic token
      final result = await _magicLoginService.authenticateWithMagicToken(widget.token, ref);

      if (result.success) {
        debugPrint('✅ Magic login successful, navigating to home');
        
        setState(() {
          _isLoading = false;
          _successMessage = result.message ?? 'Welcome back! Redirecting to your dashboard...';
        });

        // Wait a moment to show success message
        await Future.delayed(const Duration(milliseconds: 2000));

        // Navigate to home page
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/home');
        }
      } else {
        debugPrint('❌ Magic login failed: ${result.error}');
        
        setState(() {
          _isLoading = false;
          _errorMessage = result.error ?? 'Magic login failed. Please try again.';
        });
      }
    } catch (e) {
      debugPrint('❌ Error processing magic login: $e');
      
      setState(() {
        _isLoading = false;
        _errorMessage = 'An error occurred during magic login. Please try again.';
      });
    }
  }

  /// Retry magic login
  void _retryMagicLogin() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });
    
    _processMagicLogin();
  }

  /// Navigate to manual login
  void _goToManualLogin() {
    Navigator.of(context).pushReplacementNamed('/login');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor,
              AppTheme.primaryColor.withOpacity(0.8),
              AppTheme.accentColor,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                margin: const EdgeInsets.all(24.0),
                padding: const EdgeInsets.all(32.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // App Logo/Icon
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [AppTheme.primaryColor, AppTheme.accentColor],
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Icon(
                        Icons.fitness_center,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Title
                    Text(
                      'KFT Fitness',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Subtitle
                    Text(
                      'Magic Login',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Content based on state
                    if (_isLoading) ...[
                      // Loading state
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Authenticating...',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Please wait while we log you in',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                      ),
                    ] else if (_successMessage != null) ...[
                      // Success state
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Success!',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _successMessage!,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ] else if (_errorMessage != null) ...[
                      // Error state
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Login Failed',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 24),
                      
                      // Action buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: _retryMagicLogin,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Retry'),
                          ),
                          OutlinedButton(
                            onPressed: _goToManualLogin,
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppTheme.primaryColor,
                              side: BorderSide(color: AppTheme.primaryColor),
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Manual Login'),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
