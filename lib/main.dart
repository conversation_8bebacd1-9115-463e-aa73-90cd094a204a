import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:html' as html;
import 'package:webview_flutter/webview_flutter.dart';
import 'pages/profile_page.dart';
import 'pages/enhanced_login_page.dart';
import 'pages/home_page.dart';
import 'pages/workout_page.dart';

import 'pages/settings_page.dart';
import 'pages/progress_page_new.dart';
import 'services/user_service.dart';
import 'services/api_service.dart';
// Removed notification services
import 'services/progress_service.dart';
import 'services/session_manager.dart';
import 'services/persistent_auth_service.dart';
import 'services/auth_service.dart';
import 'services/single_session_auth_service.dart';
import 'services/secure_token_auth_service.dart';
import 'pages/magic_login_page.dart';
import 'design_system/kft_theme.dart';
import 'widgets/kft_bottom_navigation.dart';
import 'theme/app_theme.dart';
import 'config/app_config.dart';
import 'package:screen_protector/screen_protector.dart';
import 'dart:io' show Platform;
import 'package:provider/provider.dart';
import 'models/user_profile.dart';
import 'providers/quote_provider.dart';
import 'providers/course_settings_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'providers/auth_provider.dart';

import 'widgets/optimized_splash_screen.dart';
import 'services/auth_service.dart';
import 'services/persistent_auth_service.dart';
import 'services/auth_interceptor.dart';
import 'services/performance_monitor.dart';
import 'services/connectivity_service.dart';
import 'services/optimized_image_service.dart';
import 'services/optimized_video_service.dart';
import 'services/app_lifecycle_manager.dart';
import 'services/error_handler.dart';
import 'pages/profile_loader.dart';
import 'widgets/profile_avatar_enhanced.dart';
import 'services/bulletproof_app_manager.dart';
import 'screens/system_health_screen.dart';
// Removed awesome_notifications import

import 'services/network_resilience_service.dart';
import 'services/device_compatibility_service.dart';

import 'pages/nutrition_page.dart';
import 'services/navigation_service.dart';
import 'services/simple_pwa_service.dart';
import 'services/pwa_context_service.dart';
import 'services/lazy_loading_service.dart';
import 'services/app_shell_service.dart';
import 'widgets/simple_pwa_prompt.dart';
import 'widgets/optional_pwa_prompt.dart';
import 'services/instant_loading_service.dart';
import 'widgets/instant_loading_splash.dart';
import 'web_splash_util.dart'
    if (dart.library.html) 'web_splash_util_web.dart';

/// Custom HttpOverrides for development mode to bypass SSL certificate verification
/// WARNING: This should ONLY be used in development mode, never in production!
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (cert, host, port) {
        if (kDebugMode) {
          print('SSL Certificate bypass for development: $host:$port');
          return true; // Allow all certificates in development mode
        }
        return false; // Reject in production mode
      }
      ..connectionTimeout = const Duration(seconds: 30)
      ..idleTimeout = const Duration(seconds: 30);
  }
}

bool isLowEndDevice = false;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // ⚡ Initialize Instant Loading Service for lightning-fast startup ⚡
  final instantLoading = InstantLoadingService();

  // Start instant loading optimizations immediately
  if (kIsWeb) {
    // Web-specific optimizations
    instantLoading.initialize();
    print('⚡ Instant loading optimizations started for web');
  }

  // Initialize WebView platform for video players
  if (kIsWeb) {
    // For web platform, we don't need WebView platform initialization
    // Web platform uses HtmlElementView instead
    print('Running on web platform - using HtmlElementView for video players');
  } else {
    // Only initialize WebView platform on mobile platforms
    try {
      // This ensures WebView platform is properly initialized
      // Just check if the platform is available
      if (WebViewPlatform.instance != null) {
        print('WebView platform is available');
      }
    } catch (e) {
      print('WebView platform initialization: $e');
    }
  }

  // Set global HTTP overrides for SSL certificate bypass in development mode
  // WARNING: This should ONLY be used in development mode, never in production!
  if (kDebugMode) {
    HttpOverrides.global = MyHttpOverrides();
    print('Global SSL certificate bypass enabled for development mode');
  }

  // Set preferred orientations
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
      systemNavigationBarDividerColor: Colors.transparent,
    ),
  );

  // --- OPTIMIZATION: Parallelize critical service initialization ---
  await _initializeCriticalServicesParallel();

  // --- OPTIMIZATION: Parallelize bulletproof systems initialization ---
  await _initializeBulletproofSystemsParallel();

  // Start the app immediately
  if (kIsWeb) {
    await SimplePWAService().initialize();
    debugPrint('✅ SimplePWAService initialized in main()');

    // Initialize PWA context service for video protection
    await PWAContextService().initialize();
    debugPrint('✅ PWAContextService initialized in main()');

    // Initialize lazy loading service for performance optimization
    await LazyLoadingService().initialize();
    debugPrint('✅ LazyLoadingService initialized in main()');

    // Initialize app shell service for instant loading
    await AppShellService().initialize();
    debugPrint('✅ AppShellService initialized in main()');
  }
  runApp(const riverpod.ProviderScope(child: KFTApp()));
  hideWebSplash();
}

/// Parallelized initialization of only the most critical services
Future<void> _initializeCriticalServicesParallel() async {
  try {
    // Initialize core services first
    await AppConfig.initFromPreferences();
    await PerformanceMonitor().initialize();
    await AuthService().initialize();
    await PersistentAuthService().initialize();
    
    // Initialize user-related services
    final userService = UserService();
    await ProgressService().initialize();
    
    // Initialize other services in parallel without delays to reduce startup time
    await Future.wait([
      NetworkResilienceService().initialize(),
      DeviceCompatibilityService().initialize(),
      BulletproofAppManager().initialize(),
      SimplePWAService().initialize(),
    ]);
    // Set initialization complete (handled in _KFTAppState)
  } catch (e) {
    debugPrint('❌ Critical service initialization failed: $e');
    rethrow;
  }
}

/// Precache splash/logo image for faster splash screen
Future<void> _precacheSplashLogo() async {
  try {
    final context = WidgetsBinding.instance.renderViewElement;
    if (context != null) {
      await precacheImage(const AssetImage('assets/images/logo.png'), context);
      print('✅ Splash/logo image precached');
    }
  } catch (e) {
    print('⚠️ Could not precache splash/logo image: $e');
  }
}

/// Parallelized initialization of bulletproof systems for maximum stability
Future<void> _initializeBulletproofSystemsParallel() async {
  try {
    print('🛡️ Initializing bulletproof systems (parallel)...');
    final stopwatch = Stopwatch()..start();
    await Future.wait([
      BulletproofAppManager().initialize(),
      // --- OPTIMIZATION: Precache home screen images in background ---
      _precacheHomeScreenImages(),
      // --- OPTIMIZATION: Pre-warm isolate for DB/compute heavy work (TODO) ---
      // _prewarmIsolate(),
    ]);
    // Session manager and auth services can be initialized in parallel
    await Future.wait([
      SessionManager().initialize(
        persistentAuthService: PersistentAuthService(),
        authService: AuthService(),
      ),
    ]);
    stopwatch.stop();
    print('✅ Bulletproof systems initialized in ${stopwatch.elapsedMilliseconds}ms (parallel)');
  } catch (e) {
    print('❌ Failed to initialize bulletproof systems: $e');
  }
}

/// Precache home screen images for faster first paint
Future<void> _precacheHomeScreenImages() async {
  try {
    final context = WidgetsBinding.instance.renderViewElement;
    if (context != null) {
      // TODO: Add more images as needed for home screen
      await precacheImage(const AssetImage('assets/images/home_banner.png'), context);
      print('✅ Home screen images precached');
    }
  } catch (e) {
    print('⚠️ Could not precache home images: $e');
  }
}

/// Initialize non-critical services in the background
Future<void> _initializeBackgroundServices() async {
  try {
    print('🔄 Starting background service initialization...');
    // Stagger background service initialization to reduce CPU/memory spikes
    await Future.wait([
      ConnectivityService().initialize(),
      Future.delayed(const Duration(milliseconds: 100), () => OptimizedImageService().initialize()),
      Future.delayed(const Duration(milliseconds: 200), () => OptimizedVideoService().initialize()),
      Future.delayed(const Duration(milliseconds: 300), () => AppLifecycleManager().initialize()),
      Future.delayed(const Duration(milliseconds: 400), () => ErrorHandler().initialize()),
    ]);
    await Future.delayed(const Duration(milliseconds: 500), () => ProgressService().initialize());
    await Future.wait([
      AuthService().initialize(),
      PersistentAuthService().initialize(),
    ]);
    await Future.delayed(const Duration(milliseconds: 600), () => ApiService.initialize());
    print('API Base URL: [32m${ApiService.baseUrl}[0m');
    print('✅ All background services initialized (staggered)');
  } catch (e) {
    print('❌ Error initializing background services: $e');
  }
}

class KFTAppInstantHome extends StatelessWidget {
  const KFTAppInstantHome({super.key});

  @override
  Widget build(BuildContext context) {
    // ⚡ Use instant loading service for optimized app creation ⚡
    return InstantLoadingService().createOptimizedApp(
      title: 'KFT Fitness - Personal Training App',
      theme: AppTheme.lightTheme,
      darkTheme: KFTTheme.darkTheme,
      home: WebInstantSplash(
        child: const KFTBottomNav(),
      ),
    );
  }
}

class AuthWrapper extends riverpod.ConsumerStatefulWidget {
  final bool servicesInitialized;

  const AuthWrapper({Key? key, required this.servicesInitialized}) : super(key: key);

  @override
  _AuthWrapperState createState() => _AuthWrapperState();
}

class _AuthWrapperState extends riverpod.ConsumerState<AuthWrapper> with WidgetsBindingObserver {
  final UserService _userService = UserService();
  final AuthService _authService = AuthService();
  final PersistentAuthService _persistentAuthService = PersistentAuthService();
  bool _isLoading = true;
  bool _isLoggedIn = false;
  bool _showSplash = false; // Always false, disables splash screen

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Set global context for auth interceptor
    AuthInterceptor.globalContext = context;

    // Set context for session manager
    try {
      final sessionManager = SessionManager();
      if (sessionManager.isInitialized) {
        sessionManager.setContext(context);
      }
    } catch (e) {
      debugPrint('⚠️ Failed to set session manager context: $e');
    }

    _checkLoginStatus();
    _listenToAuthChanges();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Only check login status if we're not already logged in
    if (state == AppLifecycleState.resumed && !_isLoggedIn) {
      _checkLoginStatus();
    }
  }

  void _listenToAuthChanges() {
    _authService.authStateStream.listen((isAuthenticated) {
      if (mounted && !isAuthenticated && _isLoggedIn) {
        // User was explicitly logged out
        setState(() {
          _isLoggedIn = false;
        });

        // Navigate to login
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false,
        );
      }
    });

    // Also listen to persistent auth service changes
    _persistentAuthService.authStateStream.listen((isAuthenticated) {
      if (mounted && !isAuthenticated && _isLoggedIn) {
        // User was explicitly logged out from persistent auth service
        setState(() {
          _isLoggedIn = false;
        });

        // Navigate to login
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false,
        );
      }
    });
  }

  Future<void> _checkLoginStatus() async {
    try {
      debugPrint('🔐 Starting login status check...');

      // Wait for services to be initialized before checking auth status
      if (!widget.servicesInitialized) {
        debugPrint('🔐 Waiting for services to initialize...');
        // Wait for services with timeout
        int attempts = 0;
        while (!widget.servicesInitialized && attempts < 50) { // 5 second timeout
          await Future.delayed(const Duration(milliseconds: 100));
          attempts++;
        }
      }

      // Ensure PersistentAuthService is initialized
      if (!_persistentAuthService.isInitialized) {
        debugPrint('🔐 Initializing PersistentAuthService...');
        await _persistentAuthService.initialize();
      }

      // Check for magic login token in URL first (for instant login links)
      if (kIsWeb) {
        final magicToken = _getMagicLoginTokenFromUrl();
        if (magicToken != null) {
          debugPrint('🪄 Magic login token detected in URL, navigating to magic login page...');

          // Navigate to magic login page
          if (mounted) {
            Navigator.of(context).pushReplacementNamed('/magic-login/$magicToken');
          }
          return;
        }
      }

      // Check for secure token in URL (legacy support)
      final secureTokenService = SecureTokenAuthService();
      bool secureTokenAuth = false;

      if (kIsWeb && secureTokenService.hasSecureTokenInUrl()) {
        debugPrint('🔐 Secure token detected in URL, attempting authentication...');
        secureTokenAuth = await secureTokenService.checkAndProcessSecureToken(ref);

        if (secureTokenAuth) {
          debugPrint('✅ Secure token authentication successful');
          // Update UI immediately for instant login
          if (mounted) {
            setState(() {
              _isLoggedIn = true;
              _isLoading = false;
            });
          }
          // Load user profile in background
          _loadUserProfileInBackground();
          return; // Skip normal auth check since we're already authenticated
        }
      }

      // Now check normal authentication status
      final isLoggedIn = await _persistentAuthService.isLoggedIn();
      debugPrint('🔐 Login status check result: $isLoggedIn');

      // If logged in, validate session with session manager
      bool finalLoginStatus = isLoggedIn;
      if (isLoggedIn) {
        try {
          final sessionManager = SessionManager();
          if (sessionManager.isInitialized) {
            final validationResult = await sessionManager.validateSession();
            if (validationResult == SessionValidationResult.invalidated) {
              debugPrint('🚨 Session invalidated by session manager');
              finalLoginStatus = false;
            }
          }
        } catch (e) {
          debugPrint('⚠️ Session validation failed: $e');
          // Don't change login status on validation error
        }
      }

      // Update UI with auth status
      if (mounted) {
        setState(() {
          _isLoggedIn = finalLoginStatus;
          _isLoading = false;
        });
      }

      // Load user profile in background if logged in
      if (isLoggedIn) {
        _loadUserProfileInBackground();
      }
    } catch (e) {
      debugPrint('❌ Error checking login status: $e');
      if (mounted) {
        setState(() {
          _isLoggedIn = false;
          _isLoading = false;
        });
      }
    }
  }

  /// Load user profile and initialize notifications in background
  Future<void> _loadUserProfileInBackground() async {
    try {
      // Wait for services to be ready before loading profile
      while (!widget.servicesInitialized) {
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Get the user profile
      final userProfile = await _userService.getUserProfile();

      // Update the provider
      if (mounted) {
        final providerInstance = Provider.of<UserProfileProvider>(context, listen: false);
        providerInstance.setProfile(userProfile);
        print('Loaded profile with image URL: ${userProfile.profileImageUrl}');
      }
    } catch (e) {
      print('Error loading user profile in background: $e');
      // Don't change auth state on profile loading failure
    }
  }

  /// Extract magic login token from URL
  String? _getMagicLoginTokenFromUrl() {
    if (!kIsWeb) return null;

    try {
      final currentUrl = html.window.location.href;
      debugPrint('🪄 Checking URL for magic login token: $currentUrl');

      // Check for hash-based route: #/magic-login/TOKEN
      if (currentUrl.contains('#/magic-login/')) {
        final parts = currentUrl.split('#/magic-login/');
        if (parts.length == 2 && parts[1].isNotEmpty) {
          final token = parts[1].split('?')[0]; // Remove any query parameters
          if (token.length == 128) {
            debugPrint('🪄 Found magic login token in hash route');
            return token;
          }
        }
      }

      // Check for regular route: /magic-login/TOKEN
      if (currentUrl.contains('/magic-login/')) {
        final parts = currentUrl.split('/magic-login/');
        if (parts.length == 2 && parts[1].isNotEmpty) {
          final token = parts[1].split('?')[0]; // Remove any query parameters
          if (token.length == 128) {
            debugPrint('🪄 Found magic login token in regular route');
            return token;
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('⚠️ Error extracting magic login token from URL: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Directly show loading indicator or main app, skip splash screen
    if (_isLoading) {
      return OptimizedLoadingScreen(
        servicesInitialized: widget.servicesInitialized,
      );
    }

    // Navigate to appropriate screen based on auth status
    if (_isLoggedIn) {
      debugPrint('🏠 Navigating to main app (user is logged in)');
      return const OptionalPWAPrompt(
        showInitialPrompt: true,
        showFloatingButton: false,
        child: KFTBottomNav(),
      );
    } else {
      debugPrint('🔐 Navigating to login page (user not logged in)');
      return const OptionalPWAPrompt(
        showInitialPrompt: true,
        showFloatingButton: false,
        child: EnhancedLoginPage(),
      );
    }
  }
}

class KFTBottomNav extends StatefulWidget {
  const KFTBottomNav({super.key});

  @override
  State<KFTBottomNav> createState() => _KFTBottomNavState();
}

class _KFTBottomNavState extends State<KFTBottomNav> with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  int _selectedIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late List<Widget> _pages;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _initializePages();
    _animationController.forward();
  }

  Future<void> _initializePages() async {
    try {
      // Initialize pages
      _pages = [
        const HomePage(),
        const WorkoutPage(),
        const NutritionPage(),
        const ProfilePage(),
      ];
      // Set loading to false after initialization
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Error initializing pages: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: IndexedStack(
            index: _selectedIndex,
            children: _pages,
          ),
        ),
        bottomNavigationBar: KFTBottomNavigation(
          items: const [
            KFTBottomNavigationItem(
              label: 'Home',
              icon: Icons.home_outlined,
              activeIcon: Icons.home,
            ),
            KFTBottomNavigationItem(
              label: 'Workouts',
              icon: Icons.fitness_center_outlined,
              activeIcon: Icons.fitness_center,
            ),
            KFTBottomNavigationItem(
              label: 'Nutrition',
              icon: Icons.restaurant_menu_outlined,
              activeIcon: Icons.restaurant_menu,
            ),
            KFTBottomNavigationItem(
              label: 'Profile',
              icon: Icons.person_outline,
              activeIcon: Icons.person,
            ),
          ],
          currentIndex: _selectedIndex,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
        ),
      ),
    );
  }

  // Handle back button press
  Future<bool> _onWillPop() async {
    if (_selectedIndex != 0) {
      // If not on home page, navigate to home page
      setState(() {
        _selectedIndex = 0;
      });
      return false;
    } else {
      // If on home page, show exit confirmation dialog
      return _showExitConfirmation();
    }
  }

  // Show exit confirmation dialog
  Future<bool> _showExitConfirmation() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit App'),
        content: const Text('Are you sure you want to exit the app?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Exit'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
        ],
      ),
    ) ?? false;
  }
}

class _PlaceholderPage extends StatelessWidget {
  final String title;
  const _PlaceholderPage({required this.title});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getIconForTitle(title),
                size: 64,
                color: primaryColor,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Coming Soon',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                'We\'re working on this feature and it will be available soon.',
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                // Placeholder action
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Notify Me When Ready'),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForTitle(String title) {
    if (title.contains('Special')) {
      return Icons.category_outlined;
    } else if (title.contains('Programs')) {
      return Icons.format_list_bulleted_outlined;
    } else if (title.contains('Profile')) {
      return Icons.person_outline;
    } else if (title.contains('Calorie')) {
      return Icons.local_fire_department_outlined;
    } else {
      return Icons.star_outline;
    }
  }
}

class _ExtraFeaturesPage extends StatelessWidget {
  const _ExtraFeaturesPage();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    return Scaffold(
      appBar: AppBar(
        title: const Text('More Features'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(),
    );
  }
}

class WorkoutSessionsPage extends StatefulWidget {
  @override
  State<WorkoutSessionsPage> createState() => _WorkoutSessionsPageState();
}

class _WorkoutSessionsPageState extends State<WorkoutSessionsPage> {
  static const int totalSessions = 12;
  List<bool> unlocked = List.generate(totalSessions, (i) => i < 3);

  // Workout session thumbnails - in a real app these would be actual images
  final List<String> _thumbnails = [
    'assets/workout1.jpg',
    'assets/workout2.jpg',
    'assets/workout3.jpg',
    'assets/workout4.jpg',
    'assets/workout5.jpg',
    'assets/workout6.jpg',
    'assets/workout7.jpg',
    'assets/workout8.jpg',
    'assets/workout9.jpg',
    'assets/workout10.jpg',
    'assets/workout11.jpg',
    'assets/workout12.jpg',
  ];

  // Session titles for more engaging content
  final List<String> _sessionTitles = [
    'Core Strength',
    'HIIT Cardio',
    'Full Body Power',
    'Upper Body Focus',
    'Lower Body Burn',
    'Mobility & Stretch',
    'Endurance Builder',
    'Strength & Tone',
    'Recovery Day',
    'Power Yoga Flow',
    'Sprint Intervals',
    'Total Body Challenge',
  ];

  int get unlockedCount => unlocked.where((u) => u).length;

  void unlockSession(int index) {
    setState(() {
      unlocked[index] = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Workouts',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.notifications_outlined, color: Colors.grey.shade700),
            onPressed: () {},
          ),
        ],
      ),
      body: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
              child: _buildMotivationalBanner(context),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 20, 16),
              child: _buildMinimalProgress(context),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 20, 8),
              child: Text(
                'Your Sessions',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final isUnlocked = unlocked[index];
                  return _buildSessionListItem(context, index: index, isUnlocked: isUnlocked);
                },
                childCount: totalSessions,
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(height: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildMotivationalBanner(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withBlue(220),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Today\'s Goal',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Complete your next workout session',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Start next workout
                    final nextIndex = unlocked.indexWhere((u) => !u);
                    if (nextIndex > 0) {
                      unlockSession(nextIndex);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: theme.colorScheme.primary,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Start Now'),
                ),
              ],
            ),
          ),
          Container(
            width: 80,
            height: 80,
            decoration: const BoxDecoration(
              color: Colors.white24,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.fitness_center,
              color: Colors.white,
              size: 40,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMinimalProgress(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '$unlockedCount/$totalSessions completed',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: unlockedCount / totalSessions,
            minHeight: 4,
            backgroundColor: Colors.grey.shade200,
            color: primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildSessionListItem(BuildContext context, {required int index, required bool isUnlocked}) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    // Placeholder colors for thumbnails until real images are used
    final List<Color> placeholderColors = [
      Colors.blue.shade300,
      Colors.teal.shade300,
      Colors.green.shade300,
      Colors.amber.shade300,
      Colors.orange.shade300,
      Colors.red.shade300,
      Colors.purple.shade300,
      Colors.indigo.shade300,
      Colors.cyan.shade300,
      Colors.lime.shade300,
      Colors.pink.shade300,
      Colors.deepPurple.shade300,
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: isUnlocked
            ? () {
                showDialog(
                  context: context,
                  builder: (_) => AlertDialog(
                    title: Text(_sessionTitles[index]),
                    content: const Text('Video content placeholder.'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Close'),
                      ),
                    ],
                  ),
                );
              }
            : () {
                unlockSession(index);
              },
        child: Container(
          height: 90,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Thumbnail - replaced with a simple colored container for quick loading
              Container(
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  color: placeholderColors[index % placeholderColors.length],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    if (!isUnlocked)
                      Container(
                        color: Colors.black.withOpacity(0.5),
                        child: const Icon(
                          Icons.lock_outline,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                    if (isUnlocked)
                      Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _sessionTitles[index],
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Session ${index + 1} • ${isUnlocked ? '15 min' : 'Locked'}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Status indicator
              Padding(
                padding: const EdgeInsets.all(12),
                child: isUnlocked
                    ? Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: Colors.green.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.check,
                          color: Colors.green.shade700,
                          size: 16,
                        ),
                      )
                    : Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          shape: BoxShape.circle,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SplashScreen extends riverpod.ConsumerStatefulWidget {
  @override
  riverpod.ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends riverpod.ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    checkSession();
  }

  Future<void> checkSession() async {
    await ref.read(authProvider.notifier).checkAuth();
    final isAuthenticated = ref.read(authProvider).isAuthenticated;
    if (!isAuthenticated) {
      Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
      return;
    }
    final response = await ApiService.getProfile(context, ref);
    if (response['statusCode'] == 200) {
      Navigator.pushReplacementNamed(context, '/home');
    }
    // If 401, ApiService will handle forced logout and navigation
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}

class KFTApp extends StatefulWidget {
  const KFTApp({super.key});

  @override
  State<KFTApp> createState() => _KFTAppState();
}

class _KFTAppState extends State<KFTApp> with WidgetsBindingObserver {
  final _navigationService = NavigationService();
  bool _servicesInitialized = false;
  UserProfile? _userProfile;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _updateSystemUIOverlayStyle();
    _enableGlobalScreenProtection();

    // Detect low-end device and set global flag
    DeviceCompatibilityService().initialize().then((_) {
      setState(() {
        isLowEndDevice = DeviceCompatibilityService().isLowEndDevice;
      });
    });

    // Start only critical services for fast startup
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeBackgroundServices().then((_) {
        if (mounted) {
          setState(() {
            _servicesInitialized = true;
          });
        }
      });
    });

    // Listen for theme changes
    AppConfig.addThemeChangeListener(() {
      setState(() {
        _updateSystemUIOverlayStyle();
      });
    });

    AuthInterceptor.globalContext = context;
  }

  @override
  void dispose() {
    _disableGlobalScreenProtection();
    WidgetsBinding.instance.removeObserver(this);
    AppConfig.removeThemeChangeListener();
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    setState(() {
      _updateSystemUIOverlayStyle();
    });
  }

  void _updateSystemUIOverlayStyle() {
    final brightness = AppConfig.themeMode == ThemeMode.system
        ? WidgetsBinding.instance.platformDispatcher.platformBrightness
        : AppConfig.themeMode == ThemeMode.dark
            ? Brightness.dark
            : Brightness.light;

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: brightness == Brightness.dark ? const Color(0xFF121212) : Colors.white,
        systemNavigationBarIconBrightness: brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        systemNavigationBarDividerColor: Colors.transparent,
      ),
    );
  }

  void _enableGlobalScreenProtection() async {
    try {
      await ScreenProtector.preventScreenshotOn();
      if (Platform.isIOS) {
        ScreenProtector.addListener(() {}, (bool isCaptured) {});
      }
    } catch (e) {
      // Ignore errors
    }
  }

  void _disableGlobalScreenProtection() async {
    try {
      await ScreenProtector.preventScreenshotOff();
      if (Platform.isIOS) {
        ScreenProtector.removeListener();
      }
    } catch (e) {
      // Ignore errors
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => UserProfileProvider(),
        ),
        ChangeNotifierProvider(create: (_) => QuoteProvider()),
        ChangeNotifierProvider(create: (_) => CourseSettingsProvider()),
        ChangeNotifierProvider<ProgressService>(
          create: (_) => ProgressService(),
        ),
        Provider<UserService>(
          create: (_) => UserService(),
        ),
        Provider<NavigationService>(
          create: (_) => _navigationService,
        ),
      ],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'KFT Fitness',
        theme: AppTheme.lightTheme,
        darkTheme: KFTTheme.darkTheme,
        themeMode: AppConfig.themeMode,
        navigatorKey: _navigationService.navigatorKey,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
            child: child!,
          );
        },
        home: AuthWrapper(servicesInitialized: _servicesInitialized),
        onGenerateRoute: (RouteSettings settings) {
          // Handle magic login route
          if (settings.name != null && settings.name!.startsWith('/magic-login/')) {
            final token = settings.name!.substring('/magic-login/'.length);
            if (token.isNotEmpty) {
              return MaterialPageRoute(
                builder: (context) => MagicLoginPage(token: token),
                settings: settings,
              );
            }
          }

          // Handle hash-based routes for web
          if (settings.name != null && settings.name!.contains('#/magic-login/')) {
            final parts = settings.name!.split('#/magic-login/');
            if (parts.length == 2 && parts[1].isNotEmpty) {
              final token = parts[1];
              return MaterialPageRoute(
                builder: (context) => MagicLoginPage(token: token),
                settings: settings,
              );
            }
          }

          return null; // Let the default routing handle other routes
        },
        routes: {
          NavigationService.login: (context) => const EnhancedLoginPage(),
          NavigationService.home: (context) => const KFTBottomNav(),
          NavigationService.settings: (context) => const SettingsPage(),
          NavigationService.profile: (context) => const ProfileLoader(),
          NavigationService.progress: (context) {
            final providerInstance = Provider.of<UserProfileProvider>(context, listen: false);
            if (providerInstance.profile == null) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }
            return ProgressPageNew(user: providerInstance.profile);
          },
          NavigationService.systemHealth: (context) => const SystemHealthScreen(),
        },
      ),
    );
  }
}
