#!/bin/bash

# KFT Fitness App - Ngrok Setup Script
# This script starts ngrok and provides the URL for Flutter configuration

echo "🚀 Starting ngrok for KFT Fitness Backend..."
echo "=================================="

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed!"
    echo "Please install ngrok from: https://ngrok.com/download"
    echo ""
    echo "Quick install options:"
    echo "  macOS: brew install ngrok"
    echo "  Linux: snap install ngrok"
    echo "  Windows: Download from https://ngrok.com/download"
    exit 1
fi

# Check if port 9001 is in use
if lsof -Pi :9001 -sTCP:LISTEN -t >/dev/null ; then
    echo "✅ Backend server is running on port 9001"
else
    echo "⚠️  Warning: No server detected on port 9001"
    echo "Make sure your PHP backend is running with:"
    echo "  php -S localhost:9001 -t admin/"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo ""
echo "🌐 Starting ngrok tunnel..."
echo "Press Ctrl+C to stop ngrok"
echo ""

# Start ngrok and capture output
ngrok http 9001 --log=stdout 2>&1 | while IFS= read -r line; do
    echo "$line"
    
    # Extract the HTTPS URL when it appears
    if [[ $line == *"https://"*".ngrok-free.app"* ]]; then
        NGROK_URL=$(echo "$line" | grep -o 'https://[^[:space:]]*\.ngrok-free\.app')
        if [[ ! -z "$NGROK_URL" ]]; then
            echo ""
            echo "🎉 Ngrok tunnel is ready!"
            echo "=================================="
            echo "📱 Flutter Configuration:"
            echo "   Ngrok URL: $NGROK_URL"
            echo "   API URL: $NGROK_URL/admin/api/"
            echo ""
            echo "📋 Next steps:"
            echo "1. Copy this URL: $NGROK_URL"
            echo "2. Open Flutter app and go to Dev Tools"
            echo "3. Click 'Configure Ngrok' and paste the URL"
            echo "4. Test the connection"
            echo ""
            echo "🔗 Admin Panel: $NGROK_URL/admin/"
            echo "=================================="
            echo ""
        fi
    fi
done
